#!/usr/bin/env python3
import json

# The problematic JSON string from the user
problematic_json = '{"0d953b50-4784-11f0-9d39-6607559c9035":{"item_name":"Apartamento Dúplex 2 dormitorios (2 Adultos)","item_variant":"Tarifa Flexible || Pago Directo en el Hotel  - Solo Alojamiento","item_id":"ahZlfnNvaG8tYm91dGlxdWUtaG90ZWxzchULEghSb29tVHlwZRiAgIDk1rDLCQyiAR5zb2hvLXZpc3RhaGVybW9zYS1hcGFydGFtZW50b3M---ahZlfnNvaG8tYm91dGlxdWUtaG90ZWxzchELEgRSYXRlGICAgKS1_ZULDKIBHnNvaG8tdmlzdGFoZXJtb3NhLWFwYXJ0YW1lbnRvcw---ahZlfnNvaG8tYm91dGlxdWUtaG90ZWxzchQLEgdSZWdpbWVuGICAgLifgYEKDKIBHnNvaG8tdmlzdGFoZXJtb3NhLWFwYXJ0YW1lbnRvcw","price":154.0,"item_list_name":"Search Results","item_list_id":"search_results","item_brand":"Soho Boutique Vistahermosa Apartamentos 4* | Web Oficial | Cádiz \t","quantity":1,"category":"Room","item_category":"Apartamento Dúplex 2 dormitorios (2 Adultos)","index":1,"currency":"EUR"}}'

print('=== Debugging the JSON parsing issue ===')
print()

print('1. Testing if the JSON string is valid:')
try:
    parsed = json.loads(problematic_json)
    print('✓ JSON is valid and can be parsed by Python')
    
    # Get the item_brand value
    first_key = list(parsed.keys())[0]
    item_brand = parsed[first_key]['item_brand']
    print(f'item_brand value: {repr(item_brand)}')
    print(f'Length: {len(item_brand)}')
    print(f'Last character: {repr(item_brand[-1])}')
    print(f'Last character code: {ord(item_brand[-1])}')
    print(f'Is last char a tab? {item_brand[-1] == chr(9)}')
    
except json.JSONDecodeError as e:
    print(f'✗ JSON parse error: {e}')
    print(f'Error at position: {e.pos}')

print()
print('2. Testing what our function would produce:')

import sys
sys.path.append('.')
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

# Recreate the data structure
test_data = {
    "0d953b50-4784-11f0-9d39-6607559c9035": {
        "item_name": "Apartamento Dúplex 2 dormitorios (2 Adultos)",
        "item_brand": "Soho Boutique Vistahermosa Apartamentos 4* | Web Oficial | Cádiz \t",
        "price": 154.0
    }
}

result = dumps_json_for_javascript(test_data)
print('Our function output:')
print(repr(result))

# Simulate JavaScript template literal processing
js_processed = result.replace('\\\\"', '\\"')
print()
print('After JS template literal processing:')
print(repr(js_processed))

try:
    final_parsed = json.loads(js_processed)
    print('✓ Final result parses successfully')
    final_brand = final_parsed["0d953b50-4784-11f0-9d39-6607559c9035"]["item_brand"]
    print(f'Final item_brand: {repr(final_brand)}')
except json.JSONDecodeError as e:
    print(f'✗ Final parse error: {e}')

print()
print('3. Checking if the issue is with the actual tab character:')
# Check if there's an actual tab character vs literal \t
if '\t' in problematic_json:
    print('✓ Found actual tab character in the JSON string')
    tab_positions = [i for i, char in enumerate(problematic_json) if char == '\t']
    print(f'Tab character positions: {tab_positions}')
    for pos in tab_positions:
        context_start = max(0, pos - 10)
        context_end = min(len(problematic_json), pos + 10)
        print(f'Context around position {pos}: {repr(problematic_json[context_start:context_end])}')
else:
    print('No actual tab characters found')

if '\\t' in problematic_json:
    print('✓ Found literal \\t in the JSON string')
else:
    print('No literal \\t found')
