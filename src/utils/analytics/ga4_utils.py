import json

from booking_process.constants.advance_configs_names import DATALAYER_GA4_QUANTITY_NIGHTS, \
    DATALAYER_GA4_FORCED_CURRENCY
from booking_process.utils.booking.additional_services.additional_services_methods import get_selected_supplements
from booking_process.utils.booking.results_management.results_general import get_all_available_price_options
from booking_process.utils.booking.searchs.search_info import get_nights_searched
from booking_process.constants.session_data import SELECTED_OPTION_KEY
from booking_process.utils.compatibility.compatibility_utils import dumps_json_for_javascript
from booking_process.utils.currency.currencyUtils import get_currency
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.hotel_data import get_hotel_name
from booking_process.utils.data_management.supplements_utils import get_all_supplements
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV
from booking_process.utils.language.language_utils import get_analytics_language
from booking_process.utils.namespaces.namespace_utils import get_namespace, get_application_id
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.templates.template_utils import build_template_2

from booking_process.utils.session import session_manager


def view_items_list(datalayer_format=False):
    language = get_analytics_language()
    context = {
        'price_options': get_all_available_price_options(language),
        'currency': get_currency(),
        'datalayer_format': datalayer_format,
        'hotel_name': get_hotel_name()
    }

    context['total_room_prices'] = sum(map(lambda x: float(x.get('price')), context['price_options']))

    forced_currency_code = get_config_property_value(DATALAYER_GA4_FORCED_CURRENCY)
    if forced_currency_code:
        context['currency'] = forced_currency_code

    rendered_html = build_template_2('general/analytics/events/ga4_view_item_list.html', context)

    return rendered_html


def ga4_datalayer_transaction(reservation, total_price=0, datalayer_format=False):
    selected_options = session_manager.get(SELECTED_OPTION_KEY)
    selected_options = selected_options.split(';') if selected_options else []
    quantity_nights = get_config_property_value(DATALAYER_GA4_QUANTITY_NIGHTS)
    reservation_nights = get_nights_searched()
    language = get_analytics_language()
    currency = get_currency()
    room_items = {}
    selected_items = []
    extra_info = {}
    context = {
        'reservation': reservation,
        'hotel_code': DEV_NAMESPACE if DEV else get_namespace() or get_application_id() or '',
        'currency': currency,
        'datalayer_format': datalayer_format
    }

    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)

    if extra_info.get('price_info'):
        context['tax_amount'] = extra_info['price_info'].get('total_with_taxes') - extra_info['price_info'].get('total_without_taxes')
        context['total_price_with_tax'] = extra_info['price_info'].get('total_with_taxes')
        context['total_price_without_tax'] = extra_info['price_info'].get('total_without_taxes')
    else:
        context['total_price'] = total_price

    price_options = get_all_available_price_options(language)
    for price_option in price_options:
        quantity = reservation_nights if quantity_nights else 1
        room_items[price_option['uuid']] = _build_ecommerce_room_item(price_option, currency=currency, quantity=quantity)

    selected_options = selected_options
    for selected_uuid in selected_options:
        selected_items.append(room_items.get(selected_uuid))

    selected_supplements = get_selected_supplements(language)
    for supplement in selected_supplements:
        selected_items.append(_build_ecommerce_supplement_item(supplement, currency=currency))

    context['selected_items_json'] = dumps_json_for_javascript(selected_items)

    rendered_html = build_template_2('general/analytics/events/ga4_register_transaction.html', context)

    return rendered_html


def ecommerce_items_list(booking_step, datalayer_format):
    room_items = {}
    selected_room_items = []
    supplement_items = {}
    selected_supplement_items = []
    context = {
        'ecommerce_datalayer_format': datalayer_format
    }
    language = get_analytics_language()
    currency = get_currency()

    if booking_step >= 1:
        price_options = get_all_available_price_options(language)
        for price_option in price_options:
            room_items[price_option['uuid']] = _build_ecommerce_room_item(price_option, currency=currency)

        context['ecommerce_room_items_json'] = dumps_json_for_javascript(room_items)

    if booking_step >= 2:
        selected_options = session_manager.get(SELECTED_OPTION_KEY)
        if selected_options:
            selected_options = selected_options.split(';')
            for selected_uuid in selected_options:
                selected_room_items.append(room_items.get(selected_uuid))

            context['ecommerce_selected_room_items_json'] = dumps_json_for_javascript(selected_room_items)

        supplements = get_all_supplements(language)
        for supplement in supplements:
            supplement_items[supplement.get('key')] = _build_ecommerce_supplement_item(supplement, currency=currency)

        context['ecommerce_supplement_items_json'] = dumps_json_for_javascript(supplement_items)

    if booking_step >= 3:
        selected_supplements = get_selected_supplements(language)

        for supplement in selected_supplements:
            selected_supplement_items.append(_build_ecommerce_supplement_item(supplement, currency=currency))

        context['ecommerce_selected_supplement_items_json'] = dumps_json_for_javascript(selected_supplement_items)

    return build_template_2('general/analytics/events/ga4_ecommerce_items.html', context)


def _build_ecommerce_room_item(price_option, **kwargs):
    # Clean hotel name of any control characters (tabs, newlines, etc.)
    hotel_name = get_hotel_name()
    if hotel_name:
        # Remove control characters that could break JSON parsing
        hotel_name = hotel_name.translate(str.maketrans({
            '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
        })).strip()

    room_item = {
        'item_name': unescape(' - '.join([price_option['room_name'], price_option['rate_name'], price_option['board_name']])),
        'item_id': '---'.join([price_option['room_key'], price_option['rate_key'], price_option['board_key']]),
        'price': price_option.get('price'),
        'item_list_name': 'Search Results',
        'item_list_id': 'search_results',
        'item_brand': unescape(hotel_name) if hotel_name else '',
        'quantity': 1
    }

    for key, value in kwargs.items():
        room_item[key] = value

    return room_item


def _build_ecommerce_supplement_item(supplement, **kwargs):
    # Clean hotel name of any control characters (tabs, newlines, etc.)
    hotel_name = get_hotel_name()
    if hotel_name:
        # Remove control characters that could break JSON parsing
        hotel_name = hotel_name.translate(str.maketrans({
            '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
        })).strip()

    supplement_item = {
        'item_name': unescape(supplement.get('supplementName', supplement.get('name'))),
        'item_id': supplement.get('key'),
        'price': float(supplement.get('price')),
        'item_list_name': 'Search Results',
        'item_list_id': 'search_results',
        'item_brand': unescape(hotel_name) if hotel_name else ''
    }

    if supplement.get('days') and supplement.get('amount'):
        supplement_item['quantity'] = int(supplement['days']) * int(supplement['amount'])

    for key, value in kwargs.items():
        supplement_item[key] = value

    return supplement_item
