import os

from flask import send_from_directory

from paraty import app

# This comment is used to generate the routes for the application, if it's removed, will not be generated
#dynamic_routes_build
@app.route('/css/expr1/<path:filename>')
def custom_static_css_expr1(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/express1/template/css')
    return send_from_directory(final_path, filename)


@app.route('/js/expr1/<path:filename>')
def custom_static_js_expr1(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/express1/template/js')
    return send_from_directory(final_path, filename)


@app.route('/img/expr1/<path:filename>')
def custom_static_img_expr1(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/express1/template/img')
    return send_from_directory(final_path, filename, conditional=False)
  
@app.route('/css/sohoa/<path:filename>')
def custom_static_css_sohoa(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/sohosantacatalina/template/css')
    return send_from_directory(final_path, filename)


@app.route('/js/sohoa/<path:filename>')
def custom_static_js_sohoa(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/sohosantacatalina/template/js')
    return send_from_directory(final_path, filename)


@app.route('/img/sohoa/<path:filename>')
def custom_static_img_sohoa(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, 'webs/sohosantacatalina/template/img')
    return send_from_directory(final_path, filename, conditional=False)
  