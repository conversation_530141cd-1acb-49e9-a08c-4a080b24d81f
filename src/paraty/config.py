import os


basedir = os.path.abspath(os.path.dirname(__file__))

# fmatheis, Added for backward compatibility, might be removed in the future
os.environ['SERVER_SOFTWARE'] = 'IsolatedServer'
os.environ['NDB_USE_CROSS_COMPATIBLE_PICKLE_PROTOCOL'] = 'True'
os.environ['MEMCACHE_USE_CROSS_COMPATIBLE_PROTOCOL'] = 'True'

class Config(object):

    BASE_URL = 'https://hotel-puentereal.appspot.com'
    LANGUAGES = ['en', 'es']
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'heaven-or-hell-830umsfiwef0fjsefje2222'
    PROJECT = "soho-boutique-hotels"
    NAMESPACE = "soho-vistahermosa-apartamentos"
    LOCATION = "europe-west1"
    CLOUD_RUN_URL = None # This variable is defined on hotel deployment scripts/deploy/cloudbuild.yaml
    DEV = not os.environ.get('GAE_DEPLOYMENT_ID')
    TESTING = False
    TEST_THREADS = []
    TEMPLATES_PATH = 'templates'
    VERSION = '3.11_'  # Used to make sure we don't mix things such as cache between different versions

    # This is replaced at deploy of hotel in case that the hotel has a flexible instance
    CACHE_DB_WITH_MEMORY_SAVE = False
