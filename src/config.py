from soho_atalia_booking_widget import sohoAtaliaScript
from soho_catedral_booking_widget import sohoCatedralScript
from soho_cordoba_booking_widget import sohoCordobaScript
from soho_corporativa_booking_widget import sohoCorpoScript, sohoInjectionHandler
from soho_corporativa_booking_widget_v2 import sohoCorpoScriptv2
from soho_corporativa_booking_widget_v2 import sohoInjectionHandlerv2
from soho_equitativa_booking_widget import sohoEquitativaScript
from soho_tiburon_booking_widget import sohoTiburonScript
from utils.web.BaseInjectionHandler import InjectionWidgetHandler
from webs.sohosantacatalina.templateHandler import Temp<PERSON><PERSON><PERSON><PERSON> as catalinaHandler

applicationId = "soho-boutique-hotels"
template_handler = None


# Todo - create an injection script for all individual hotels to replace repeated one-to-one scripts
extra_routing = {
	'.*localhost.*': {
		'.*sohoataliascript.*': sohoAtaliaScript,
		'.*sohoataliawidget.*': InjectionWidgetHandler,
		'.*sohotiburonscript.*': sohoTiburonScript,
		'.*sohotiburonwidget.*': InjectionWidgetHandler,
		'.*sohocordobascript.*': sohoCordobaScript,
		'.*sohocordobawidget.*': InjectionWidgetHandler,
		'.*sohocatedralscript.*': sohoCatedralScript,
		'.*sohocatedralwidget.*': InjectionWidgetHandler,
		'.*sohoequitativascript.*': sohoEquitativaScript,
		'.*sohoequitativawidget.*': InjectionWidgetHandler,
		'.*sohosignaturescriptv2.*': sohoCorpoScriptv2,
		'.*sohosignaturewidgetv2.*': sohoInjectionHandlerv2,
		'.*': catalinaHandler
	},

	'.*cordoba.*': {
		'.*sohocordobascript.*': sohoCordobaScript,
		'.*sohocordobawidget.*': InjectionWidgetHandler,
		'.*': catalinaHandler,
	},

	'.*catedral.*': {
		'.*sohocatedralscript.*': sohoCatedralScript,
		'.*sohocatedralwidget.*': InjectionWidgetHandler,
		'.*': catalinaHandler,
	},

	'.*equitativa.*': {
		'.*sohoequitativascript.*': sohoEquitativaScript,
		'.*sohoequitativawidget.*': InjectionWidgetHandler,
		'.*': catalinaHandler,
	},

	'.*atalia.*': {
		'.*sohoataliascript.*': sohoAtaliaScript,
		'.*sohoataliawidget.*': InjectionWidgetHandler,
		'.*': catalinaHandler,
	},

	'.*tiburon.*': {
		'.*sohotiburonscript.*': sohoTiburonScript,
		'.*sohotiburonwidget.*': InjectionWidgetHandler,
		'.*': catalinaHandler,
	},

	'.*(sohohotels|signature|corpo).*': {
		'.*sohosignaturescriptv2.*': sohoCorpoScriptv2,
		'.*sohosignaturewidgetv2.*': sohoInjectionHandlerv2,
		'.*sohosignaturescript.*': sohoCorpoScript,
		'.*sohosignaturewidget.*': sohoInjectionHandler,
		'.*': catalinaHandler,
	},
	'.*catalina.*': {
		'.*': catalinaHandler
	},
	'.*': {
		'.*': catalinaHandler
	},
}

flexible_server_type = True
server_instances = 1
server_cpu = 1
server_memory = 4

templates = ["express1", "sohosantacatalina"]






