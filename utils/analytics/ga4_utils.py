"""
    This module contains the methods to build the html code for the GA4 events.
"""

import json
import logging
from datetime import datetime
from flask import request

from booking_process.constants.advance_configs_names import DATALAYER_GA4_QUANTITY_NIGHTS, \
    DATALAYER_GA4_FORCED_CURRENCY, SEPARATE_CLUB_RATES_IN_DATALAYER, AVOID_CURRENCY_IN_DATALAYER_ITEMS, \
    ONLY_PRICES_WITHOUT_TAX_IN_DATALAYER, INCLUDE_TAXES_IN_GA_VALUES, EUR_CONVERSION_DATALAYER, \
    DATALAYER_ADDITIONAL_EVENTS, USER_RATES_LOCKER, RATES_LOCK_VERSION
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT
from booking_process.constants.web_configs_names import GA4_EXTRA_PARAMS_BY_EVENT, ANALYTICS_EVENT_CONSTANTS, \
    CLUB_CONFIG
from booking_process.utils.analytics.analyticsUtils import complete_context_with_club_info
from booking_process.utils.analytics.analytics_constants import VIEW_ITEM_LIST, BEGIN_CHECKOUT, PURCHASE, \
    NO_AVAILABILITY_EVENT, EVENT_ROOT_PREFIX, DEFAULT_ITEM_LIST_NAME, DEFAULT_ITEM_LIST_ID, USER_TYPE_MEMBER, \
    USER_TYPE_REGULAR, SEARCH_TYPE_HOTEL, ROOM_TYPE, PACKAGE_TYPE
from booking_process.utils.auditing import auditUtils
from booking_process.utils.booking.additional_services.additional_services_methods import get_selected_supplements
from booking_process.utils.booking.bookingUtils import build_reservation_price_info
from booking_process.utils.booking.results_management.results_general import get_all_available_price_options
from booking_process.utils.booking.searchs.search_info import get_nights_searched, get_num_persons
from booking_process.constants.session_data import SELECTED_OPTION_KEY, CURRENT_SEARCH, ERROR_PAGE_DISPLAYED, \
    SEARCH_KEY_PREFIX, ADDITIONAL_SERVICES_SEEKER_RESULTS, BOOKING_STEP, PRICE_OPTION_KEY_PREFIX, \
    PRICE_OPTION_KEY_PREFIX_V2
from booking_process.utils.booking.searchs.search_utils import retrieve_search_from_history
from booking_process.utils.clubs.rates_management import get_all_club_rates, separate_allowed_forbidden_club_rates
from booking_process.utils.compatibility.compatibility_utils import dumps_json_for_javascript
from booking_process.utils.currency.currencyUtils import get_currency
from booking_process.utils.currency.currency_conversor import CurrencyConversor
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.hotel_data import get_hotel_name
from booking_process.utils.data_management.rates_data import get_rate_from_key
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.supplements_utils import get_all_supplements
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.dates.dates_management import get_num_days
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.encryption.encryptionUtils import hash_string
from booking_process.utils.language.language_utils import get_analytics_language
from booking_process.utils.namespaces.namespace_utils import get_namespace, get_application_id, get_hotel_code
from booking_process.utils.templates.template_utils import build_template_2

from booking_process.utils.session import session_manager
from booking_process.constants.advance_configs_names import DEFAULT_CURRENCY
from booking_process.utils.users.users_methods import club_is_active, get_user_logged
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_configuration_properties


def view_items_list(datalayer_format=False, type=None):
    language = get_analytics_language()
    rendered_html = ''
    MAX_PRICE_OPTIONS = 20

    context = {
        'currency': get_currency(),
        'datalayer_format': datalayer_format,
        'hotel_name': get_hotel_name(),
        'separate_club_rates': get_config_property_value(SEPARATE_CLUB_RATES_IN_DATALAYER),
        'avoid_currency_in_items': get_config_property_value(AVOID_CURRENCY_IN_DATALAYER_ITEMS),
        'extra_params': get_extra_params_by_event(language, type).get(VIEW_ITEM_LIST, {}),
        'event_root_prefix': EVENT_ROOT_PREFIX,
        'default_item_list_name': DEFAULT_ITEM_LIST_NAME,
        'default_item_list_id': DEFAULT_ITEM_LIST_ID
    }

    price_options = get_all_available_price_options(language)

    if type:
        price_options = list(filter(lambda x: (
                (type == ROOM_TYPE and not x.get('package_name'))
                or (type == PACKAGE_TYPE and x.get('package_name'))
        ), price_options))

        context['list_type'] = type

    if club_is_active():
        price_options = _club_filter_price_options(price_options, language)

    _apply_ga4_item_list_configuration(price_options, get_namespace())

    context['price_options'] = price_options
    context['total_room_prices'] = sum(map(lambda x: float(x.get('price')), context['price_options']))

    forced_currency_code = get_config_property_value(DATALAYER_GA4_FORCED_CURRENCY)
    if forced_currency_code:
        context['currency'] = forced_currency_code

    if not type or type == ROOM_TYPE:
        rendered_html += build_template_2('general/analytics/events/ga4_view_item_list_functions.html', context)

    if len(context['price_options']) > MAX_PRICE_OPTIONS:
        price_options_blocks = [context['price_options'][i:i + MAX_PRICE_OPTIONS] for i in
                                range(0, len(context['price_options']), MAX_PRICE_OPTIONS)]
        for price_options in price_options_blocks:
            context['price_options'] = price_options
            context['loop_index'] = price_options_blocks.index(price_options)
            rendered_html += build_template_2('general/analytics/events/ga4_view_item_list.html', context)

    else:
        rendered_html += build_template_2('general/analytics/events/ga4_view_item_list.html', context)

    return rendered_html

def _filter_club_rates(price_options: list[dict], allowed_rates: list[dict], language: str) -> list[dict]:
    """
    Filter price options to only include allowed club rates.
    
    This function filters the price options list twice:
    1. First to get only club rates
    2. Then to ensure they are in the allowed rates list

    :param price_options: List of price option dictionaries to filter
    :param allowed_rates: List of allowed club rate dictionaries
    :param language: Language code for rate key lookup
    :return: Filtered list containing only allowed club rates
    """
    return list(filter(
        lambda x: get_rate_from_key(x.get('rate_key'), language).get('identifier') in allowed_rates,
        list(filter(lambda x: x.get('is_club_rate'), price_options))
    ))

def _filter_non_club_rates(price_options: list[dict]) -> list[dict]:
    """
    Filter price options to exclude all club rates.
    
    This function removes any price option that has is_club_rate set to True.

    :param price_options: List of price option dictionaries to filter
    :return: Filtered list containing only non-club rates
    """
    return list(filter(lambda x: not x.get('is_club_rate'), price_options))

def _mark_club_rates_as_locked(price_options: list[dict]) -> list[dict]:
    """
    Mark all club rates in the price options list as locked.
    
    This function modifies the input list by adding locked_rate=True to all club rates.
    The original list is modified in place.

    :param price_options: List of price option dictionaries to process
    :return: The modified list with locked club rates
    """
    for price_option in price_options:
        if price_option.get('is_club_rate'):
            price_option['locked_rate'] = True
    return price_options

def _handle_toggle_discount(price_options: list[dict], club_config: dict, allowed_rates: list[dict], language: str) -> list[dict]:
    """
    Handle toggle discount logic for club rates.
    
    This function processes the club discount toggle configuration and returns the appropriate
    filtered price options based on the user's choice and club configuration.

    :param price_options: List of price option dictionaries to process
    :param club_config: Club configuration dictionary
    :param allowed_rates: List of allowed club rate dictionaries
    :param language: Language code for rate key lookup
    :return: Filtered list based on toggle discount configuration
    """
    choice_club_discount = request.values.get('choice_club_discount')
    if choice_club_discount == 'disabled' or (not choice_club_discount and club_config.get('toggle_disabled')):
        return _filter_non_club_rates(price_options)
    return _filter_club_rates(price_options, allowed_rates, language)

def _club_filter_price_options(price_options: list[dict], language: str) -> list[dict]:
    """
    Filter price options based on club membership status and configuration.
    
    This function implements the main logic for filtering price options based on:
    - User login status
    - Club configuration
    - Rate locker settings
    - Toggle discount settings
    
    The function follows these rules:
    1. If user rates locker is disabled, filter based on user login status
    2. If toggle discount is enabled, apply toggle discount logic
    3. If lock without prices is enabled, filter based on user login status
    4. For non-logged users, mark club rates as locked
    
    :param price_options: List of price option dictionaries to filter
    :param language: Language code for rate key lookup
    :return: Filtered and/or modified list of price options based on club rules
    :raises: No explicit exceptions are raised
    """
    user_is_logged = get_user_logged()
    allowed_rates, _ = separate_allowed_forbidden_club_rates(get_all_club_rates())
    club_config = get_web_configuration(CLUB_CONFIG)
    
    if not get_config_property_value(USER_RATES_LOCKER):
        if not user_is_logged:
            return (_filter_club_rates(price_options, allowed_rates, language) 
                   if get_section_from_section_spanish_name("_club user pestana", language)
                   else _filter_non_club_rates(price_options))
        return _filter_club_rates(price_options, allowed_rates, language)

    lock_version = get_config_property_value(RATES_LOCK_VERSION)
    if lock_version == 'toggle_discount':
        return _handle_toggle_discount(price_options, club_config, allowed_rates, language)

    if club_config and club_config.get('lock_without_prices'):
        return (_filter_non_club_rates(price_options) if not user_is_logged 
                else _filter_club_rates(price_options, allowed_rates, language))

    if not user_is_logged:
        return _mark_club_rates_as_locked(price_options)
    
    return _filter_club_rates(price_options, allowed_rates, language)

def _apply_ga4_item_list_configuration(price_options: list[dict], namespace: str) -> None:
    """
    Apply GA4 view item list configuration mappings to price options.
    
    This function handles:
    1. Getting integration configuration properties for GA4 view item list
    2. Handling corporate-specific configurations if present
    3. Applying property mappings to price options based on configuration

    :param price_options: List of price option dictionaries to modify. Each dictionary should contain the properties to be mapped according to the GA4 configuration.
    :param namespace: The namespace identifier used to fetch the GA4 configuration. This determines which set of property mappings to apply.
    :return: None. The function modifies the price_options list in place.
    """
    if not (items_xml := get_integration_configuration_properties('ga4_view_item_list', namespace)):
        return

    if 'from_corporate' in items_xml:
        items_xml = get_integration_configuration_properties('ga4_view_item_list', items_xml['from_corporate'])
        if not items_xml:
            return

    for name, value in items_xml.items():
        for price_option in price_options:
            if price_option.get(name) and price_option.get(value):
                price_option[name] = price_option[value]

def no_availability(datalayer_format=False):
    language = get_analytics_language()
    event_constants = get_web_configuration(ANALYTICS_EVENT_CONSTANTS) or {}

    context = {
        'event_name': event_constants.get('no_availability_event') or NO_AVAILABILITY_EVENT,
        'currency': get_currency(),
        'datalayer_format': datalayer_format,
        'extra_params': get_extra_params_by_event(language).get(NO_AVAILABILITY_EVENT, {})
    }

    forced_currency_code = get_config_property_value(DATALAYER_GA4_FORCED_CURRENCY)
    if forced_currency_code:
        context['currency'] = forced_currency_code

    rendered_html = build_template_2('general/analytics/events/ga4_no_availability.html', context)
    return rendered_html


def view_advanced_supplements_list(datalayer_format:bool|None=False)-> str:

    available_supplements = session_manager.get(ADDITIONAL_SERVICES_SEEKER_RESULTS) or []

    supplement_items = {
        supplement["key"]: _build_ecommerce_supplement_item(supplement)
        for supplement in available_supplements
    }

    total_supplements = sum(item.get("price", 0) * item.get("quantity", 1) for item in supplement_items.values())

    context = {
        'supplement_items': json.dumps(supplement_items),
        'currency': get_currency(),
        'datalayer_format': datalayer_format,
        'hotel_name': get_hotel_name(),
        'total_supplements': total_supplements
    }

    return build_template_2('general/analytics/events/ga4_view_advanced_supplements_list.html',
                            context) if supplement_items else ''


def booking_error(datalayer_format=False):
    search_params = session_manager.get(SEARCH_KEY_PREFIX) or retrieve_search_from_history()

    context = {
        'datalayer_format': datalayer_format,
        'session_id': session_manager.get_session_id() or '',
        'unexistent_session': not session_manager.session_exists(),
        'expired_session': session_manager.session_expired(),
        "error_page_displayed": session_manager.get(ERROR_PAGE_DISPLAYED) if session_manager.session_exists() else False,
        "search_params": json.dumps(search_params),
    }
    fire_error = context.get('unexistent_session') or context.get('expired_session') or context.get('error_page_displayed')
    return build_template_2('general/analytics/events/ga4_booking_error.html', context) if fire_error else ''


def view_hotel_list(datalayer_format: bool = False) -> str:
    """
    Build view_hotel_list event for analytics

    :param datalayer_format: True if has to use datalayer, otherwise False
    :return: Event builded
    """

    language = get_analytics_language()
    search = session_manager.get(CURRENT_SEARCH) or {}
    current_search = search.get('currentSearch', {})

    context = {
        'search_availabilty': any((hotel.get('price') for hotel in search.get('hotels', []))),
        'no_availabilty_redirect': bool(search.get('no_dispo_from_booking1', '')),
        'promocode': current_search.get('promoCode') or '',
        'hotel_name': get_hotel_name(),
        'namespace': get_hotel_code(),
        'occupancy': [f"{room.get('numAdults', 2)}-{room.get('numKids', 0)}-{room.get('numBabies', 0)}" for room in current_search.get('rooms', [])],
        'rooms_number': len(current_search.get('rooms', [])),
        'total_nights': get_num_days(current_search) or 0,
        'adults_number': sum((int(room.get('numAdults', 0)) for room in current_search.get('rooms', []))),
        'children_number': sum((int(room.get('numKids', 0)) for room in current_search.get('rooms', []))),
        'currency_code': get_currency(),
        'destiny': search.get('destiny', ''),
        'hotels': [hotel['namespace'] for hotel in search.get('hotels', []) if hotel.get('namespace')],
        'hotels_searched': search.get('hotels', []),
        'datalayer_format': datalayer_format
    }

    return build_template_2('general/analytics/events/ga4_view_hotel_list.html', context)


def ga4_begin_checkout(datalayer_format=False):
    language = get_analytics_language()
    context = {
        'currency': get_currency(),
        'datalayer_format': datalayer_format,
        'extra_params': get_extra_params_by_event(language).get(BEGIN_CHECKOUT, {}),
        'event_root_prefix': EVENT_ROOT_PREFIX
    }
    selected_items = _get_selected_items_list(language)
    context['selected_items_json'] = dumps_json_for_javascript(selected_items)

    rendered_html = build_template_2('general/analytics/events/ga4_ecommerce_begin_checkout.html', context)

    return rendered_html


def ga4_datalayer_transaction(reservation, total_price=0, datalayer_format=False):
    language = get_analytics_language()
    currency = get_currency()
    context = {
        'reservation': reservation,
        'hotel_code': DEV_NAMESPACE if DEV else get_namespace() or get_application_id() or '',
        'currency': currency,
        'datalayer_format': datalayer_format,
        'prices_whitout_tax_only': get_config_property_value(ONLY_PRICES_WITHOUT_TAX_IN_DATALAYER),
        'extra_params': get_extra_params_by_event(language).get(PURCHASE, {}),
        'event_root_prefix': EVENT_ROOT_PREFIX
    }

    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo) or {}
        price_info = extra_info.get('price_info', {})

        if price_info:
            process_price_info_context(context, price_info, currency)
        else:
            context['total_price'] = total_price

        if extra_info.get("ds_click_id"):
            context['extra_params']["dsclid"] = extra_info.get("ds_click_id")

    if reservation.email:
        context['extra_params']['email_sha256'] = hash_string(reservation.email)

    if reservation.telephone:
        context['extra_params']['phone_number_sha256'] = hash_string(reservation.telephone)

    selected_items = _get_selected_items_list(language)

    context['selected_items_json'] = dumps_json_for_javascript(selected_items)

    rendered_html = build_template_2('general/analytics/events/ga4_register_transaction.html', context)

    return rendered_html


def process_price_info_context(context, price_info, currency):
    total_without_taxes = float(price_info.get('total_without_taxes', 0))
    total_with_all_taxes = float(price_info.get('total_with_all_taxes', 0))
    total_with_taxes = float(price_info.get('total_with_taxes', 0))

    tax_amount = total_with_taxes - total_without_taxes
    context.update({
        'total_price_without_tax': total_without_taxes,
        'total_price_with_tax': total_with_taxes,
        'tax_amount': tax_amount
    })

    include_taxes_config = get_config_property_value(INCLUDE_TAXES_IN_GA_VALUES) or ""
    available_config_values = {"country", "accommodation", "all"}
    extra_params = context.get("extra_params", {})

    if include_taxes_config in available_config_values:
        if include_taxes_config == "all":
            all_tax_amount = total_with_all_taxes - total_without_taxes
            extra_params.update({
                'ga_total_with_all_taxes': total_with_all_taxes,
                'ga_tax_amount': all_tax_amount
            })

        elif include_taxes_config in {"country", "accommodation"}:
            tax_type = price_info.get("taxes", {}).get(include_taxes_config, {})
            if not tax_type.get("included"):
                additional_tax_amount = float(tax_type.get('value', 0))
                new_tax_amount = tax_amount + additional_tax_amount
                extra_params.update({
                    'ga_tax_amount': new_tax_amount,
                    f'ga_total_with_{include_taxes_config}_tax': total_without_taxes + new_tax_amount
                })

    default_currency = get_config_property_value(DEFAULT_CURRENCY)
    eur_conversion_datalayer = get_config_property_value(EUR_CONVERSION_DATALAYER)
    if eur_conversion_datalayer and default_currency != "EUR":
        try:
            eur_currency_exchange = CurrencyConversor(currency, 'EUR').currency_exchange

            def convert_to_eur(amount):
                return amount / eur_currency_exchange if eur_currency_exchange else 0

            all_taxes_amount = total_with_all_taxes - total_without_taxes
            extra_params["eur_converted_prices"] = {
                "total_with_all_taxes": convert_to_eur(total_with_all_taxes),
                "total_without_taxes": convert_to_eur(total_without_taxes),
                "all_taxes_amount": convert_to_eur(all_taxes_amount),
            }
        except Exception as e:
            logging.error(f"Error during currency conversion: {e}")


def ecommerce_items_list(booking_step, datalayer_format):
    room_items = {}
    selected_room_items = []
    supplement_items = {}
    selected_supplement_items = []

    language = get_analytics_language()
    context = {
        'ecommerce_datalayer_format': datalayer_format,
        'extra_params': dumps_json_for_javascript(get_extra_params_by_event(language))
    }
    currency = get_currency()

    if booking_step >= 1:
        for price_option in get_all_available_price_options(language):
            room_items[price_option['uuid']] = _build_ecommerce_room_item(price_option)

        context['ecommerce_room_items_json'] = dumps_json_for_javascript(room_items)

    if booking_step >= 2:
        selected_options = session_manager.get(SELECTED_OPTION_KEY)
        if selected_options:
            selected_options = selected_options.split(';')
            for selected_uuid in selected_options:
                selected_room_items.append(room_items.get(selected_uuid))

            context['ecommerce_selected_room_items_json'] = dumps_json_for_javascript(selected_room_items)

        supplements = get_all_supplements(language)
        for supplement in supplements:
            supplement_items[supplement.get('key')] = _build_ecommerce_supplement_item(supplement)

        context['ecommerce_supplement_items_json'] = dumps_json_for_javascript(supplement_items)

    if booking_step >= 3:
        selected_supplements = get_selected_supplements(language)

        for supplement in selected_supplements:
            selected_supplement_items.append(_build_ecommerce_supplement_item(supplement))

        context['ecommerce_selected_supplement_items_json'] = dumps_json_for_javascript(selected_supplement_items)

    context['selected_currency'] = currency

    additional_events = get_config_property_value(DATALAYER_ADDITIONAL_EVENTS)
    if additional_events:
        additional_events = additional_events.split(';')
        context['additional_events'] = json.dumps(additional_events)

    return build_template_2('general/analytics/events/ga4_ecommerce_items.html', context)


def _build_ecommerce_room_item(price_option, **kwargs):
    # Clean hotel name of any control characters (tabs, newlines, etc.)
    hotel_name = get_hotel_name()
    if hotel_name:
        # Remove control characters that could break JSON parsing
        hotel_name = hotel_name.translate(str.maketrans({
            '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
        })).strip()

    room_item = {
        'item_name': unescape(price_option['room_name'].replace('\"', '')),
        'item_variant': unescape(' - '.join([price_option['rate_name'], price_option['board_name']]).replace('\"', '')),
        'item_id': '---'.join([price_option['room_key'], price_option['rate_key'], price_option['board_key']]),
        'price': price_option.get('price'),
        'item_list_name': DEFAULT_ITEM_LIST_NAME,
        'item_list_id': DEFAULT_ITEM_LIST_ID,
        'item_brand': unescape(hotel_name) if hotel_name else '',
        'quantity': 1,
        'category': 'Room',
        'item_category': unescape(price_option['item_category']),
        'index': price_option['index'],
    }

    if price_option.get('offer_name'):
        room_item['item_category2'] = unescape(price_option['offer_name'].replace('\"', ''))

    if price_option.get('custom_items_categories'):
        room_item.update(price_option.get('custom_items_categories'))

    if price_option.get('package_name'):
        room_item['item_name'] = unescape(' - '.join([price_option['room_name'], price_option['rate_name'],
                                             price_option['board_name'], price_option['package_name']]).replace('\"', ''))

    if not get_config_property_value(AVOID_CURRENCY_IN_DATALAYER_ITEMS):
        room_item['currency'] = get_currency()

    for key, value in kwargs.items():
        room_item[key] = value

    return room_item


def _build_ecommerce_supplement_item(supplement, **kwargs):
    event_constants = get_web_configuration(ANALYTICS_EVENT_CONSTANTS) or {}

    # Clean hotel name of any control characters (tabs, newlines, etc.)
    hotel_name = get_hotel_name()
    if hotel_name:
        # Remove control characters that could break JSON parsing
        hotel_name = hotel_name.translate(str.maketrans({
            '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
        })).strip()

    supplement_item = {
        'item_name': unescape(supplement.get('supplementName', supplement.get('name'))),
        'item_id': supplement.get('key'),
        'price': float(supplement.get('price')) if supplement.get('price') else float(supplement.get('base_price', 0)),
        'item_list_name': event_constants.get('services_list_name') or DEFAULT_ITEM_LIST_NAME,
        'item_list_id': event_constants.get('services_list_id') or DEFAULT_ITEM_LIST_ID,
        'item_brand': unescape(hotel_name) if hotel_name else '',
        'category': 'Supplement',
        'usuario': event_constants.get('regular_user') or USER_TYPE_REGULAR
    }

    complete_context_with_club_info(supplement_item)
    if 'user_club_id' in supplement_item:
        supplement_item['user_type'] = event_constants.get('registered_user') or USER_TYPE_MEMBER

    if supplement.get('days') and supplement.get('amount'):
        supplement_item['quantity'] = int(supplement['days']) * int(supplement['amount'])

    if not get_config_property_value(AVOID_CURRENCY_IN_DATALAYER_ITEMS):
        supplement_item['currency'] = get_currency()

    for key, value in kwargs.items():
        supplement_item[key] = value

    return supplement_item


def _get_selected_items_list(language):
    price_options = get_all_available_price_options(language)
    reservation_nights = get_nights_searched()
    quantity_nights = get_config_property_value(DATALAYER_GA4_QUANTITY_NIGHTS)

    room_items = {}
    selected_items = []
    selected_options = session_manager.get(SELECTED_OPTION_KEY)
    selected_options = selected_options.split(';') if selected_options else []

    for price_option in price_options:
        quantity = reservation_nights if quantity_nights else 1
        room_items[price_option['uuid']] = _build_ecommerce_room_item(price_option, quantity=quantity)

    for selected_uuid in selected_options:
        selected_items.append(room_items.get(selected_uuid))

    selected_supplements = get_selected_supplements(language)
    for supplement in selected_supplements:
        selected_items.append(_build_ecommerce_supplement_item(supplement))

    return selected_items


def get_extra_params_by_event(language, type=None):
    try:
        extra_params_by_event = get_web_configuration(GA4_EXTRA_PARAMS_BY_EVENT)
        extra_params = {}

        current_search = session_manager.get(CURRENT_SEARCH)
        event_constants = get_web_configuration(ANALYTICS_EVENT_CONSTANTS) or {}

        if extra_params_by_event and current_search:
            current_search = current_search.get('currentSearch')

            search_type = event_constants.get('hotel_search_name') or SEARCH_TYPE_HOTEL
            start_date = datetime.strptime(current_search.get('startDate'), SEARCH_DATE_FORMAT)
            end_date = datetime.strptime(current_search.get('endDate'), SEARCH_DATE_FORMAT)
            nights = (end_date - start_date).days
            rooms = len(current_search.get('rooms', []))
            currency = get_currency()
            forced_currency_code = get_config_property_value(DATALAYER_GA4_FORCED_CURRENCY)
            if forced_currency_code:
                currency = forced_currency_code

            selectedPrice = []
            total_price = None
            tax = None
            selectedOption = session_manager.get(SELECTED_OPTION_KEY)
            discount = 0
            if selectedOption:
                for partialSelected in selectedOption.split(";"):
                    selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

                    selected_price_v2 = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + partialSelected)
                    if selected_price_v2 and isinstance(selected_price_v2.get("promotion"), dict):
                        discount += selected_price_v2.get("promotion").get("value", 0)

                price_info = build_reservation_price_info(selectedPrice)
                total_price = price_info.get('total_without_taxes')
                total_with_taxes = price_info.get('total_with_taxes')
                tax = round(float(total_with_taxes) - float(total_price), 2)

            booking_step = session_manager.get(BOOKING_STEP)
            list_id = None
            list_name = None

            if booking_step in [1,3,4]:
                list_id = event_constants.get('search_list_id')
                list_name = event_constants.get('search_list_name')

                if(type == PACKAGE_TYPE):
                    list_id = event_constants.get('packs_list_id')
                    list_name = event_constants.get('packs_list_name')

            if booking_step == 2:
                list_id = event_constants.get('services_list_id')
                list_name = event_constants.get('services_list_name')

            available_params = {
                'promocode': current_search.get('promoCode'),
                'namespace': get_namespace() or get_application_id() or '',
                'hotel_name': get_hotel_name(),
                'search_type': search_type,
                'start_date_dash': datetime.strftime(start_date, '%d-%m-%Y'),
                'end_date_dash': datetime.strftime(end_date, '%d-%m-%Y'),
                'nights': nights,
                'adults': get_num_persons(current_search, only_adults=True),
                'children': get_num_persons(current_search, only_kids=True),
                'babies': get_num_persons(current_search, only_babies=True),
                'guests': get_num_persons(current_search),
                'rooms': rooms,
                'user_type': event_constants.get('regular_user') or USER_TYPE_REGULAR,
                'list_id': list_id or DEFAULT_ITEM_LIST_ID,
                'list_name': list_name or DEFAULT_ITEM_LIST_NAME,
                'currency': currency,
                'total_price': total_price,
                'tax': tax,
                'discount': discount
            }

            complete_context_with_club_info(available_params)
            if 'user_club_id' in available_params:
                available_params['user_type'] = event_constants.get('registered_user') or USER_TYPE_MEMBER

            if start_date:
                today_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                available_params['days_in_advance'] = (start_date - today_datetime).days

            for event, config in extra_params_by_event.items():
                event_config = get_web_configuration(extra_params_by_event.get(event))

                if event_config:
                    event_extra_params = {}
                    for key, value in event_config.items():
                        param_value = available_params.get(value.replace('@@', ''))
                        if param_value is not None:
                            event_extra_params[key] = param_value

                    extra_params[event] = event_extra_params

        return extra_params

    except Exception as e:
        logging.error("Error getting ga4 extra params by event: %s", e)
        message = auditUtils.makeTraceback()
        notify_exception('[Analytics] Error getting ga4 extra params by event: ', message, add_hotel_info=True)
        return {}