import logging, json

from flask import request

from booking_process.components import render_hooks
from booking_process.constants.advance_configs_names import DEFAULT_WEB_LANGUAGE
from booking_process.constants.web_configs_names import BOOKING_TRANSFER_SERVICE
from booking_process.constants.session_data import LANGUAGE
from booking_process.utils.booking.additional_services.additional_services_utils import is_booking2_react_active
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.supplements_utils import get_all_services_images_info, get_all_supplements
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary, get_default_language
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.transfer_service.constants import AVAILABLE_TRANSFER_DIRECTIONS, TRANSFER_SERVICE, \
    TRANSFER_SERVICE_CANCELLED, TRANSFER_SERVICE_MODIFIED, TRANSFER_DIRECTION_BOTH, TRANSFER_DIRECTION_TO, \
    TRANSFER_DIRECTION_FROM
from booking_process.utils.session import session_manager


def build_transfer_form_context(supplements, language):
    transfer_service_config = get_web_configuration(BOOKING_TRANSFER_SERVICE)

    if transfer_service_config:

        context = {}
        if transfer_service_config.get('form_data'):
            translations = get_web_dictionary(language)
            context = get_transfer_form_fields(transfer_service_config.get('form_data'), language)

            selected_directions_dict = {}
            transfer_supplements = get_supplements_with_transfer_data(supplements, language)

            for supplement in transfer_supplements:
                transfer_direction = supplement.get('transfer_direction', '').lower()
                for direction in AVAILABLE_TRANSFER_DIRECTIONS:
                    if direction == transfer_direction or transfer_direction == TRANSFER_DIRECTION_BOTH:
                        logging.info("Detected transfer for direction '%s'" % transfer_direction)
                        direction_context = {
                            'direction': direction,
                            'form_title': supplement.get('transfer_%s_title' % direction) or translations.get('T_transfer_%s' % direction),
                            'form_toggle_label': supplement.get('transfer_%s_toggle_label' % direction) or translations.get('T_transfer_%s_no_flight' % direction),
                        }
                        selected_directions_dict[direction] = direction_context

            for direction in AVAILABLE_TRANSFER_DIRECTIONS:
                if selected_directions_dict.get(direction):
                    context['selected_directions'].append(selected_directions_dict[direction])

        if not context.get('selected_directions'):
            return {}

        extra_info_transfers_form = render_hooks('extra_info_transfers_form', context.get('flight_fields'), _return_type=dict)
        if extra_info_transfers_form:
            context.update(extra_info_transfers_form)

        return context
    return {}


def get_transfer_form_fields(transfer_section, language):
    context = {
        'selected_directions': [],
        'flight_fields': [],
        'extra_fields': []
    }

    transfer_form_data = get_section_from_section_spanish_name(transfer_section, language)
    transfer_form_data.update(get_properties_for_entity(transfer_form_data.get('key', False), language))
    transfer_form_fields = get_pictures_from_section_name(transfer_section, language)
    for field in transfer_form_fields:
        field.update(get_properties_for_entity(field.get('key', False), language))
        if field.get('field_type') == 'flight':
            context['flight_fields'].append(field)
        else:
            context['extra_fields'].append(field)

    if transfer_form_data.get('content'):
        context['extra_text'] = unescape(transfer_form_data['content'])

    return context


def get_supplements_with_transfer_data(supplement_list: list[dict], language: str = SPANISH) -> list[dict]:
    """
    Get all supplements with transfer data

    :param supplement_list: List of supplements
    :param language: Language of the supplements
    :return: List of supplements with transfer data
    :unit_test: unit_tests.booking_process.utils.transfer_service.test_transfer_service_utils.TestTransferServiceUtils.test_get_supplements_with_transfer_data
    """
    all_services_image_info = get_all_services_images_info(language)
    transfer_supplement = []

    for supplement in supplement_list:
        target_key = supplement.get('key') or supplement.get('service_key')
        supplement_props = all_services_image_info.get(target_key, {})

        if supplement_props.get('transfer'):
            supplement.update(supplement_props)
            transfer_supplement.append(supplement)

    return transfer_supplement


def update_info_transfer_services(transfer_service_config, supplements, request):
    language = session_manager.get(LANGUAGE)

    email_fields_section = transfer_service_config.get('email_fields')
    if not email_fields_section:
        email_fields_section = transfer_service_config.get('form_data')

    if email_fields_section:
        section_fields = get_transfer_form_fields(email_fields_section, language)
        flight_fields = section_fields.get('flight_fields', []) + [{'title': 'transfer_disabled'}]

        for supplement in supplements:
            if not supplement.get('extra_info') or not supplement['extra_info'].get('transfer_data_direction'):
                continue

            current_direction = supplement['extra_info']['transfer_data_direction']
            if current_direction in AVAILABLE_TRANSFER_DIRECTIONS or current_direction == TRANSFER_DIRECTION_BOTH:
                transfer_service_data = {}
                if supplement.get('transfer_direction') == TRANSFER_DIRECTION_BOTH:
                    transfer_service_data[TRANSFER_DIRECTION_FROM] = get_raw_flights_data(flight_fields, TRANSFER_DIRECTION_FROM, request)
                    transfer_service_data[TRANSFER_DIRECTION_TO] = get_raw_flights_data(flight_fields, TRANSFER_DIRECTION_TO, request)
                else:
                    transfer_service_data[current_direction] = get_raw_flights_data(flight_fields, current_direction, request)

                transfer_service_data['extra_info_fields'] = get_raw_extra_transfer_data(section_fields.get('extra_fields', []), request)

                supplement['extra_info']['transfer_data'] = transfer_service_data

    return supplements


def build_extra_info_transfer_services(transfer_service_config: dict[str, str], supplements: list[dict]) -> list[dict]:
    """
    Build extra info for transfer services from selected supplements separated by direction.

    :param transfer_service_config: Transfer service config from web configs.
    :param supplements: List of selected supplements.
    :return: List of extra info for transfer services.
    """
    language = get_default_language()
    translations = get_web_dictionary(language)

    selected_directions = get_selected_transfer_by_direction(supplements)
    logging.info('Selected directions for transfer services: %s' % selected_directions)

    extra_info_fields = []
    if email_fields_section := transfer_service_config.get('email_fields') or transfer_service_config.get('form_data'):
        section_fields = get_transfer_form_fields(email_fields_section, language)
        flight_fields = section_fields.get('flight_fields', []) + [{'title': 'transfer_disabled'}]
        for direction, supplement_info in selected_directions.items():
            extra_info_fields.append(get_form_data_by_direction(direction, supplement_info, flight_fields, translations))

        extra_info_fields.append({'extra_info_fields': get_extra_transfer_services_info(section_fields.get('extra_fields', []))})

    logging.info('All extra info from transfer ordered by category: %s' % extra_info_fields)

    return extra_info_fields


def build_transfer_context(context, transfer_service):
    transfer_service_config = get_web_configuration(BOOKING_TRANSFER_SERVICE)

    if transfer_service_config.get('show_transfer_data_in_confirmation'):
        transfer_service = transfer_service
        transfer_data = {}
        extra_info = {}

        for transfer_value in transfer_service:
            for key, data in transfer_value.items():
                if key in AVAILABLE_TRANSFER_DIRECTIONS:
                    transfer_data[key] = data
                else:
                    extra_info.update({key: data})

        context['transfer_service_data'] = [{'transfer_data': transfer_data}, extra_info]


def get_selected_transfer_by_direction(supplements):
    selected_directions = {}
    for supplement in supplements:
        if supplement.get('transfer_direction') and (
                supplement.get('transfer_direction') in AVAILABLE_TRANSFER_DIRECTIONS or supplement.get(
                'transfer_direction') == TRANSFER_DIRECTION_BOTH):
            selected_quantity = supplement.get('amount')
            if supplement.get('transfer_direction') == TRANSFER_DIRECTION_BOTH:
                selected_directions[TRANSFER_DIRECTION_TO] = [supplement.get('name'), selected_quantity]
                selected_directions[TRANSFER_DIRECTION_FROM] = [supplement.get('name'), selected_quantity]
            else:
                selected_directions[supplement.get('transfer_direction')] = [supplement.get('name'), selected_quantity]
    return selected_directions


def get_form_data_by_direction(
        direction: str, supplement_info: list[str|int], flight_fields: list[dict], translations: dict[str, str]
) -> dict[str, dict]:
    """
    Get the transfers selected data from request values by direction.

    :param direction: Current direction to get the data from.
    :param supplement_info: Information about the supplement selected. [name, quantity]
    :param flight_fields: Custom transfer form fields.
    :param translations: All translations for the current language.
    :return: The transfer data for the current direction.
    """
    direction_context = {
        'service_name': supplement_info[0],
        'service_quantity': supplement_info[1],
        'service_info': {}
    }

    for field in flight_fields:
        field_title = field.get('title') + '_' + direction
        if field_title in ['transfer_disabled_to', 'transfer_disabled_from']:
            field_showing_name = translations.get('T_informacion_vuelo')
            if request.values.get(field_title):
                direction_context['service_info'][field_showing_name] = 'PENDING'
        else:
            field_showing_name = field.get('email_name') or field.get(f'custom_text_{direction}') or field.get('description')
            if not field_showing_name:
                continue

            if request.values.get(field_title):
                direction_context['service_info'][field_showing_name] = request.values.get(field_title)

    return {direction: direction_context}


def get_raw_flights_data(flight_fields, direction, request):
    data_dict = {}
    for field in flight_fields:
        if field.get('title') == 'transfer_disabled':
            continue
        field_title = field.get('title') + '_' + direction
        if request.values.get(field_title):
            data_dict[field.get('title')] = request.values.get(field_title)
        else:
            data_dict[field.get('title')] = 'T_pending'

    return data_dict


def get_raw_extra_transfer_data(extra_fields_list, request):
    extra_info_services = {}
    for extra_field in extra_fields_list:
        field_title = extra_field.get('title')
        if request.values.get(field_title):
            extra_info_services[field_title] = request.values.get(field_title)

    return extra_info_services


def get_extra_transfer_services_info(extra_fields_list: list[dict]) -> dict:
    """
    Get extra transfer services info from request values.

    :param extra_fields_list: List of extra fields to get info from.
    :return: Dictionary with extra info services. Keys are the field showing names and values are the values from the request.
    :unit_test: unit_tests.booking_process.utils.transfer_service.test_transfer_service_utils.TestTransferServiceUtils.test_get_extra_transfer_services_info
    """
    extra_info_services = {}

    for extra_field in extra_fields_list:
        field_showing_name = extra_field.get('email_name') or extra_field.get('description')
        if not field_showing_name:
            continue

        if field_value := request.values.get(extra_field.get('title')):
            extra_info_services[field_showing_name] = field_value

    return extra_info_services


def get_transfer_service_conditions(language, identifier=None):
    transfer_conditions_section_name = get_web_configuration(BOOKING_TRANSFER_SERVICE).get('service_conditions')
    if transfer_conditions_section_name:
        transfer_conditions_section = get_section_from_section_spanish_name(transfer_conditions_section_name, language)
        if transfer_conditions_section and transfer_conditions_section.get('content'):
            logging.info('Adding transfer service conditions in costumer reservation email')

            content = transfer_conditions_section.get('content')
            if identifier:
                content = content.replace('@@@localizador@@@', identifier).replace('@@@identifier@@@', identifier)

            return content


def check_external_transfer_provider_service(original_reservation, new_reservation):
    original_reservation_extra_info = json.loads(original_reservation.extraInfo) if original_reservation.extraInfo else {}
    new_reservation_extra_info = json.loads(new_reservation.extraInfo) if new_reservation.extraInfo else {}

    if original_reservation_extra_info.get(TRANSFER_SERVICE) and not new_reservation_extra_info.get(TRANSFER_SERVICE):
        logging.info('Reservation modification - Old reservation has External Transfer Service but new one does not')
        session_manager.set(TRANSFER_SERVICE_CANCELLED, original_reservation_extra_info.get(TRANSFER_SERVICE))
    if original_reservation_extra_info.get(TRANSFER_SERVICE) and new_reservation_extra_info.get(TRANSFER_SERVICE):
        logging.info('Reservation modification - Old reservation has External Transfer Service and new one too')
        session_manager.set(TRANSFER_SERVICE_MODIFIED, True)


def process_manager_modification(reservation, reservation_extra_info, language=SPANISH):
    logging.info('Processing modification from manager')
    transfer_extra_info = []

    is_modification = False
    is_cancellation = False

    if reservation_extra_info.get('additional_services_keys'):
        logging.info('Checking supplement selected')
        selected_services_list = reservation_extra_info.get('additional_services_keys').split(';')
        available_transfer_services = get_supplements_with_transfer_data(get_all_supplements(language), language)

        for service in selected_services_list:
            service_info = service.split(' - ')
            if service_info:
                key = service_info[0].strip()
                for transfer in available_transfer_services:
                    if transfer.get('key') == key:
                        if transfer.get('transfer_direction'):
                            if transfer.get('transfer_direction').lower() == TRANSFER_DIRECTION_FROM or transfer.get('transfer_direction').lower() == TRANSFER_DIRECTION_BOTH:
                                transfer_extra_info.append({
                                    TRANSFER_DIRECTION_FROM: {
                                        'service_name': transfer.get('supplementName'),
                                        'service_quantity': service_info[-3].split(':')[1].strip(),
                                        'service_info': {
                                            get_web_dictionary(language).get('T_informacion_vuelo'): 'PENDING'
                                        }
                                    }
                                })
                            if transfer.get('transfer_direction') == TRANSFER_DIRECTION_TO or transfer.get('transfer_direction') == TRANSFER_DIRECTION_BOTH:
                                transfer_extra_info.append({
                                    TRANSFER_DIRECTION_TO: {
                                        'service_name': transfer.get('supplementName'),
                                        'service_quantity': service_info[-3].split(':')[1].strip(),
                                        'service_info': {
                                            get_web_dictionary(language).get('T_informacion_vuelo'): 'PENDING'
                                        }
                                    }
                                })

                            logging.info('Transfer founded. Rebuilding extra info for transfer services from manager: %s' % transfer_extra_info)
                            if reservation_extra_info.get(TRANSFER_SERVICE):
                                is_modification = True
                                original_info = reservation_extra_info.get(TRANSFER_SERVICE)
                                if len(original_info) == 3:
                                    transfer_extra_info.append({
                                        'extra_info_fields': reservation_extra_info.get(TRANSFER_SERVICE)[2].get('extra_info_fields')
                                    })

                            reservation_extra_info[TRANSFER_SERVICE] = transfer_extra_info

                            return is_modification, is_cancellation, transfer_extra_info
                        else:
                            logging.warning(f'There are some transfers services on reservation {reservation.identifier}, but they dont have transfer direction! Avoid provider notification and send alert to account')
                            actual_namespace = get_namespace()
                            message = f'Modification was not processed for reservation {reservation.identifier} in {actual_namespace} due to transfer_direction property is missing for service {transfer.get("supplementName")}. \n Please add transfer_direction property and process modification or cancellation with the transfer provider manually'
                            notify_exception('[Account] Missing transfer_direction property', message)
                            return

    if reservation_extra_info.get(TRANSFER_SERVICE):
        logging.info('Original resevation used to have transfers but not anymore. We have to recuperate original info from reservation to send email')
        is_cancellation = True
        transfer_extra_info = reservation_extra_info.get(TRANSFER_SERVICE)

    reservation_extra_info[TRANSFER_SERVICE] = transfer_extra_info

    return is_modification, is_cancellation, transfer_extra_info


def get_transfer_service_for_extra_info(supplements: list[dict]) -> list|None:
    """
    Get transfer service info for extra info from supplements.
    It can be retrieved from session if it's a gateway redirection to avoid data loss by redirections.

    :param supplements: List of selected supplements.
    :return: The transfer service info for extra info or None if there is no transfer service or it's a booking2 react flow.
    :unit_test: unit_tests.booking_process.utils.transfer_service.test_transfer_service_utils.TestTransferServiceUtils.test_get_transfer_service_for_extra_info
    """

    if is_booking2_react_active():
        return None

    transfer_supplements = get_supplements_with_transfer_data(supplements, SPANISH)
    transfer_service_config = get_web_configuration(BOOKING_TRANSFER_SERVICE)

    if not transfer_supplements or not transfer_service_config:
        return None

    if transfer_info_from_session := session_manager.get(TRANSFER_SERVICE):
        # Needed for gateway redirections
        logging.info(f'Transfer info found in session. Transfer info: {transfer_info_from_session}')
        return transfer_info_from_session

    return build_extra_info_transfer_services(transfer_service_config, transfer_supplements)
