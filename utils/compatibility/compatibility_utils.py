import json


def dumps_json_for_javascript(content):
    """
    Dumps JSON with proper escaping for use in JavaScript template literals in Django templates.

    This function double-escapes quotes and other special characters so that when the JSON
    is embedded in Django template literals like `JSON.parse(\`{{ json_data|safe }}\`)`,
    the resulting JavaScript receives properly escaped JSON that can be parsed correctly.

    <PERSON>perly handles UTF-8 characters (like accented characters) preserving them in their
    original form without double-encoding issues.

    Note: The output is NOT valid JSON for Python's json.loads() due to double-escaped quotes.
    This is intentional, the double-escaping is consumed by JavaScript template literal processing.

    :param content: Content with JSON structure to dumps and clean.
    :return: JSO<PERSON> dumped with correct escaping for JS template literals.
    :unit_test: unit_tests.booking_process.utils.compatibility.test_compatibility_utils.TestCompatibilityUtils.test_dumps_json_for_javascript
    """

    # Generate clean JSON - use ensure_ascii=False to preserve unicode characters
    dumped_content = json.dumps(content, ensure_ascii=False, separators=(',', ':'))

    # Replace problematic whitespace characters that could break JavaScript
    cleaned_content = dumped_content.translate(str.maketrans({
        '\t': ' ', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
    }))

    # In template literals, \" becomes " so we need \\" to get \" in the final JSON
    cleaned_content = cleaned_content.replace('\\"', '\\\\"')

    # We need to escape them as \\` to create a valid JSON string that contains \`
    cleaned_content = cleaned_content.replace('`', '\\\\`')

    return cleaned_content
