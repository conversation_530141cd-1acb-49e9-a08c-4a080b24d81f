@import url(https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap);@import url(https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap);#booking2-flight-app-root{color:var(--text_color,#333);position:relative}#booking2-flight-app-root *{box-sizing:border-box}#booking2-flight-app-root :not(i){font-family:var(--font-text,"Open Sans",sans-serif)}#booking2-flight-app-root .currencyValue{margin-right:3px}.loader-wrapper{align-items:center;display:flex;flex-direction:column;gap:20px;height:100%;justify-content:center;padding:20px;width:100%}.loader-wrapper .loader-message{color:var(--text_color,#333)}.loader-wrapper .loader,.loader-wrapper .loader:after,.loader-wrapper .loader:before{animation:loading 1.5s .5s infinite;aspect-ratio:1;box-shadow:0 0 0 3px inset var(--text_color,#333);position:relative;width:35px}.loader-wrapper .loader:after,.loader-wrapper .loader:before{animation-delay:1s;content:"";left:calc(100% + 5px);position:absolute}.loader-wrapper .loader:after{animation-delay:0s;left:-40px}@keyframes loading{0%,55%,to{border-radius:0}20%,30%{border-radius:50%}}.selected-flight-banner{align-items:center;background-color:#fff;border:1px solid var(--booking_color_1_-50,#003864);border-radius:12px;box-sizing:border-box;display:flex;font-family:var(--font-text,sans-serif);gap:16px;justify-content:space-between;margin:16px auto;padding:12px 32px;width:1140px}.selected-flight-banner .product-details-wrapper .banner-title{font-size:16px;font-weight:600;letter-spacing:.32px;line-height:20px;margin-bottom:8px}.selected-flight-banner .product-details-wrapper .product-details{font-size:16px;letter-spacing:.32px;line-height:20px}.selected-flight-banner .price-wrapper{text-align:right}.selected-flight-banner .price-wrapper .current-price{align-items:end;display:flex;font-size:24px;font-weight:600;gap:5px;justify-content:end}.selected-flight-banner .price-wrapper .current-price span:not(.selected_differential_price){color:var(--booking_color_1_-50,#003864)}.selected-flight-banner .price-wrapper .current-price .selected_differential_price,.selected-flight-banner .price-wrapper .current-price .selected_differential_price span{color:var(--booking_color_1,#456ba7);font-size:20px}.selected-flight-banner .price-wrapper .price-message{font-size:10px;letter-spacing:.2px}@media screen and (max-width:767px){.selected-flight-banner{flex-direction:column;width:calc(100% - 20px)}.selected-flight-banner .product-details-wrapper .product-details{font-size:14px}.selected-flight-banner .price-wrapper{text-align:left;width:100%}.selected-flight-banner .price-wrapper .price-info .current-price{justify-content:start}}.flights-results-wrapper{box-sizing:border-box;display:flex;flex-direction:column;font-family:var(--font-text,"Open Sans",sans-serif);gap:16px;margin:12px auto;width:1140px}.flights-results-wrapper .load-more-btn{background-color:#fff;border:1px solid var(--booking_color_1,#08c);border-radius:8px;color:var(--booking_color_1,#08c);cursor:pointer;font-size:16px;font-weight:700;margin:16px auto 0;padding:8px 32px}.flights-results-wrapper .load-more-btn:hover{opacity:.8}.flights-results-wrapper .flight-info-card{background-color:#fff;border:1px solid #dfe0e4;border-radius:12px;position:relative;width:100%}.flights-results-wrapper .flight-info-card .outside-card{cursor:pointer;display:flex;padding:16px 12px;width:100%}.flights-results-wrapper .flight-info-card.recommended{border:2px solid var(--booking_color_1,#08c);border-radius:12px 0 12px 12px;margin-top:30px}.flights-results-wrapper .flight-info-card.recommended .recommended-label{background-color:var(--booking_color_1,#08c);border-radius:10px 10px 0 0;color:#fff;font-size:16px;font-weight:700;letter-spacing:.4px;padding:4px 24px;position:absolute;right:-2px;top:0;transform:translateY(-100%)}.flights-results-wrapper .flight-info-card.selected{border:2px solid #3ab793;border-radius:0 12px 12px 0}.flights-results-wrapper .flight-info-card.selected .selected-label{background-color:#3ab793;border:2px solid #3ab793;border-radius:10px 0 0 10px;bottom:-2px;color:#fff;font-size:16px;font-weight:700;height:calc(100% + 4px);left:-32px;letter-spacing:.4px;padding:24px 4px;position:absolute;text-align:center;top:-2px;width:30px}.flights-results-wrapper .flight-info-card.selected .selected-label .selected-text{bottom:0;left:-85px;position:absolute;right:0;transform:rotate(-90deg) translate(120%);transform-origin:bottom right}.flights-results-wrapper .flight-info-card .flight{padding:0 24px;position:relative;width:calc(50% - 95px)}.flights-results-wrapper .flight-info-card .flight:after{background-color:#dfe0e4;content:"";height:100px;position:absolute;right:0;top:50%;transform:translateY(-50%);width:1px}.flights-results-wrapper .flight-info-card .flight .flight-header i{font-size:16px;margin-right:8px}.flights-results-wrapper .flight-info-card .flight .flight-header span{font-size:16px;letter-spacing:.32px;line-height:20px}.flights-results-wrapper .flight-info-card .flight .flight-header .flight-label{text-transform:uppercase}.flights-results-wrapper .flight-info-card .flight .flight-header .flight-date{font-weight:700;text-transform:capitalize}.flights-results-wrapper .flight-info-card .flight .flight-info-container{display:flex;gap:16px;justify-content:space-between;margin-top:16px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .airline-data{display:flex;flex-direction:column;gap:8px;justify-content:center;text-align:center;width:70px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .airline-data .airline-logo{max-width:100%;object-fit:contain}.flights-results-wrapper .flight-info-card .flight .flight-info-container .airline-data i{font-size:30px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .airline-data .airline-name{font-size:14px;font-weight:300;letter-spacing:.28px;text-transform:capitalize}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary{align-items:center;display:flex;gap:12px;justify-content:space-between}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper{display:flex;flex-direction:column;gap:4px;width:40%}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper:first-of-type{align-items:end}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper:first-of-type .airport-name{text-align:right}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .hour{font-size:22px;font-weight:700;letter-spacing:.44px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .airport-code,.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .airport-name{font-size:16px;letter-spacing:.32px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .airport-code{font-weight:300}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .flight-duration-wrapper{align-items:center;display:flex;flex-direction:column;font-size:12px;gap:8px;position:relative}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .flight-duration-wrapper:before{background-color:var(--booking_color_1,#08c);content:"";height:2px;left:0;position:absolute;top:50%;transform:translateY(-50%);width:100%}.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .flight-duration-wrapper .flight-duration,.flights-results-wrapper .flight-info-card .flight .flight-info-container .itinerary .flight-duration-wrapper .scales{width:max-content}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras{align-items:center;display:flex;flex-direction:column;gap:8px;justify-content:center;position:relative}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.cabin-luggage-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.checked-luggage-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.hand-luggage-included{width:16px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.cabin-luggage-not-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.checked-luggage-not-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras svg.hand-luggage-not-included{width:20px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip{background-color:#fff;border:1px solid #dfe0e4;border-radius:12px;box-shadow:0 -5px 25px -5px #0000001a,0 -5px 10px -5px #0000000a;left:50%;max-width:300px;padding:8px 16px;position:absolute;top:-10px;transform:translate(-50%,-100%);width:max-content;z-index:1}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-title{font-size:12px;font-weight:700;margin-bottom:8px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details{display:flex;flex-direction:column;gap:4px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item{align-items:center;display:flex;gap:4px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.cabin-luggage-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.checked-luggage-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.hand-luggage-included{width:12px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.cabin-luggage-not-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.checked-luggage-not-included,.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .icon svg.hand-luggage-not-included{width:16px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-luggage-details .luggage-item .title{font-size:12px}.flights-results-wrapper .flight-info-card .flight .flight-info-container .extras-tooltip .tooltip-bottom-text{border-top:1px solid #dfe0e4;font-size:12px;margin-top:8px;padding-top:4px}.flights-results-wrapper .flight-info-card .price-details-wrapper{align-items:end;display:flex;flex-direction:column;justify-content:center;width:190px}.flights-results-wrapper .flight-info-card .price-details-wrapper .price-wrapper{text-align:right}.flights-results-wrapper .flight-info-card .price-details-wrapper .price-wrapper .price-flight{font-size:32px;font-weight:500;line-height:40px}.flights-results-wrapper .flight-info-card .price-details-wrapper .price-wrapper .price-flight .monedaConv{font-size:16px}.flights-results-wrapper .flight-info-card .price-details-wrapper .price-wrapper .price-package{font-size:10px;font-weight:300;letter-spacing:.2px}.flights-results-wrapper .flight-info-card .price-details-wrapper .see-more-btn{background-color:#fff;border:1px solid var(--booking_color_1,#08c);border-radius:8px;color:var(--booking_color_1,#08c);cursor:pointer;font-size:16px;font-weight:700;margin:16px 0 0 auto;padding:8px 32px}.flights-results-wrapper .flight-info-card .price-details-wrapper .see-more-btn:hover{opacity:.8}.flights-results-wrapper .flight-info-card .flight-modal-info{height:100vh;max-height:100vh;overflow:hidden}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper{background-color:#fff;height:100vh;max-height:100vh;padding:16px 40px 32px;position:fixed;right:0;top:0;width:876px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .close-modal-button{font-size:24px;left:40px;right:auto;top:25px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .close-modal-button i{color:var(--booking_color_1,#08c)}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup{height:100%;max-height:100%;overflow:hidden}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header{align-items:center;display:flex;justify-content:space-between;margin-left:40px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .popup-title{font-size:20px;font-weight:500;letter-spacing:.4px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .select-flight-btn{background:#0000 linear-gradient(74deg,var(--booking_color_1_-50,#003864) 0,var(--booking_color_1,#456ba7) 100%) 0 0 no-repeat padding-box;border:none;border-radius:10px;color:#fff;cursor:pointer;font-size:20px;font-weight:700;letter-spacing:.4px;padding:12px 32px;text-transform:uppercase}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .select-flight-btn:hover{opacity:.8}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .select-flight-btn[disabled]{cursor:default;opacity:.5}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .select-flight-btn i.fa-spinner{animation:rotate 1s linear infinite;color:#fff}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper{border:1px solid #a7a8a9;border-radius:8px;margin-top:16px;max-height:calc(100vh - 116px);overflow-y:auto}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .alert-dates,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .duration,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .scales{font-size:16px;font-weight:500;letter-spacing:.32px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .alert-dates i,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .duration i,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .scales i{color:var(--text_color,#333);font-size:24px;margin-right:4px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .scales{color:#3ab793}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .alert-dates{color:#e4ac50}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper{border-bottom:1px solid #a7a8a9;padding:24px 40px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details{align-items:center;display:flex;justify-content:space-between}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route{font-size:16px;font-weight:500;letter-spacing:.32px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route .title{margin-bottom:10px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route .title .label{background-color:var(--booking_color_1,#08c);border-radius:9999px;color:#fff;display:inline-block;font-weight:700;margin-left:10px;padding:5px 20px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route .icons{align-items:center;display:flex;gap:25px;justify-content:flex-start}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .price-details{text-align:right}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .price-details .price-flight{font-size:32px;font-weight:500;line-height:40px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .price-details .price-flight .monedaConv{font-size:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .price-details .price-package{font-size:10px;font-weight:300;letter-spacing:.2px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header{align-items:center;display:flex;flex-wrap:wrap;justify-content:space-between;margin-bottom:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header i{font-size:16px;margin-right:8px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header span{font-size:16px;letter-spacing:.32px;line-height:20px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-label{text-transform:uppercase}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-date{font-weight:700;text-transform:capitalize}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-header-right{align-items:center;display:flex;gap:20px;justify-content:space-between}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-header-bottom{margin-top:5px;text-align:right;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block{display:grid;grid-template-areas:"itinerary-details airline-data" "duration duration";grid-template-columns:calc(100% - 120px) 120px;justify-content:space-between}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details{grid-area:itinerary-details;padding-left:16px;position:relative}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-duration-line{background-color:var(--booking_color_1,#08c);height:50%;left:0;position:absolute;top:20px;width:1px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-duration-line:after,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-duration-line:before{background-color:var(--booking_color_1,#08c);border-radius:50%;content:"";height:10px;left:-4px;position:absolute;width:10px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-duration-line:before{top:-4px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-duration-line:after{bottom:-4px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-main-details{display:grid;gap:4px 32px;grid-template-areas:"hour city" "date airport";grid-template-columns:calc(40% - 16px) calc(60% - 16px);justify-content:start;margin-bottom:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-main-details .hour{font-size:20px;font-weight:700;grid-area:hour;letter-spacing:.4px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-main-details .city{font-size:20px;font-weight:500;grid-area:city}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-main-details .date{font-size:14px;grid-area:date;letter-spacing:.28px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .itinerary-details .flight-main-details .airport-name-code{font-size:14px;grid-area:airport;letter-spacing:.28px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data{align-items:end;display:flex;flex-direction:column;gap:8px;gap:0;grid-area:airline-data;justify-content:center;text-align:center;width:70px;width:120px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data .airline-logo{max-width:100%;object-fit:contain}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data i{font-size:30px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data .airline-name{display:block;font-size:14px;font-weight:300;letter-spacing:.28px;margin-top:12px;text-transform:capitalize}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data .airline-name,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data .flight-code,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data .flight-operation{font-size:12px;font-weight:400;letter-spacing:.24px;line-height:18px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-operation{width:max-content}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-operation span{text-transform:capitalize}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-duration{padding-left:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-duration span{font-size:14px;font-style:italic;letter-spacing:.28px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-duration span.duration{font-weight:500}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .scales-info{border-bottom:1px solid #a7a8a9;border-top:1px solid #a7a8a9;font-size:14px;font-style:italic;letter-spacing:.28px;margin:12px -12px;padding:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .scales-info span{font-weight:500}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper{display:flex}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details{padding:24px 40px;width:50%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details{border-right:1px solid #a7a8a9;width:50%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper{align-items:center;display:flex;gap:12px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper:not(:last-of-type){margin-bottom:8px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper{align-items:center;display:flex;justify-content:center;width:32px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.cabin-luggage-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.checked-luggage-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.hand-luggage-included{width:24px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.cabin-luggage-not-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.checked-luggage-not-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.hand-luggage-not-included{width:28px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-info-wrapper{display:flex;flex-direction:column}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-info-wrapper .luggage-title{font-size:12px;letter-spacing:.24px;line-height:18px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-info-wrapper .luggage-description{font-size:10px;letter-spacing:.2px;line-height:18px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details .extra-wrapper{align-items:center;display:flex;gap:12px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details .extra-wrapper .extra-icon{font-size:24px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details .extra-wrapper .extra-title{font-size:14px;letter-spacing:.28px;line-height:20px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details .extra-wrapper:not(:last-of-type){margin-bottom:8px}@media screen and (max-width:767px){.flights-results-wrapper{width:100%}.flights-results-wrapper .flight-info-card.selected{border-radius:12px 0 12px 12px;margin-top:30px}.flights-results-wrapper .flight-info-card.selected .selected-label{border-radius:10px 10px 0 0;height:fit-content;left:auto;padding:4px 24px;position:absolute;right:-2px;top:0;transform:translateY(-100%);width:fit-content}.flights-results-wrapper .flight-info-card.selected .selected-label .selected-text{left:0;position:relative;transform:none;transform-origin:unset}.flights-results-wrapper .flight-info-card .outside-card{flex-direction:column;padding:8px 12px}.flights-results-wrapper .flight-info-card .outside-card .flight,.flights-results-wrapper .flight-info-card .outside-card .price-details-wrapper{width:100%}.flights-results-wrapper .flight-info-card .outside-card .flight{border-bottom:1px solid #dfe0e4;display:flex;gap:16px;justify-content:space-between;padding:8px 0}.flights-results-wrapper .flight-info-card .outside-card .flight:after{height:50px;right:210px}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-header{align-items:center;display:grid;gap:0 8px;grid-template-areas:"icon label" "icon date";justify-content:center}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-header i{font-size:22px;grid-area:icon;margin-right:0}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-header .flight-label{font-size:14px;grid-area:label;letter-spacing:.28px;line-height:14px}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-header .flight-date{font-size:12px;font-weight:400;grid-area:date;letter-spacing:.24px;line-height:12px}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-info-container{margin-top:0;width:200px}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-info-container .itinerary{width:100%}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .airport-code{order:0}.flights-results-wrapper .flight-info-card .outside-card .flight .flight-info-container .itinerary .itinerary-info-wrapper .hour{order:1}.flights-results-wrapper .flight-info-card .outside-card .price-details-wrapper{align-items:center;display:flex;flex-direction:row;justify-content:space-between;margin:12px 0 8px}.flights-results-wrapper .flight-info-card .outside-card .price-details-wrapper .see-more-btn{margin:4px 0 0}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper{padding:32px 16px 104px;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .close-modal-button{left:auto;right:40px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup{height:auto;max-height:none;overflow-y:auto}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header{gap:16px;margin-left:0;margin-right:64px;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-header .popup-title{font-size:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper{max-height:none;overflow:hidden}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details{flex-direction:column;gap:8px;padding:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route{width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .general-details .flight-route .title .label{margin:5px 0}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper{padding:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-header-left{width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-header-right{margin-top:5px;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .flight-header .flight-header-bottom{margin-top:5px;text-align:left;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block{grid-template-areas:"itinerary-details itinerary-details" "airline-data airline-data" "duration duration"}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .airline-data{align-items:start;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .itinerary-wrapper .itinerary-block .flight-duration{padding-left:0}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper{flex-direction:column}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .more-details{padding:16px;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details{border-bottom:1px solid #a7a8a9;border-right:none}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper{width:30px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.cabin-luggage-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.checked-luggage-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.hand-luggage-included{width:22px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.cabin-luggage-not-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.checked-luggage-not-included,.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .flight-details-wrapper .extras-details-wrapper .luggage-details .luggage-wrapper .luggage-icon-wrapper svg.hand-luggage-not-included{width:26px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom{align-items:end;background-color:#fff;bottom:0;box-shadow:0 -5px 25px -5px #0000001a,0 -5px 10px -5px #0000000a;display:flex;gap:16px;justify-content:space-between;left:0;max-height:86px;padding:16px;position:fixed;width:100%}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .select-flight-btn{background:#0000 linear-gradient(74deg,var(--booking_color_1_-50,#003864) 0,var(--booking_color_1,#456ba7) 100%) 0 0 no-repeat padding-box;border:none;border-radius:10px;color:#fff;cursor:pointer;font-size:16px;font-weight:700;letter-spacing:.4px;padding:12px 16px;text-transform:uppercase}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .select-flight-btn:hover{opacity:.8}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .select-flight-btn[disabled]{cursor:default;opacity:.5}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .select-flight-btn i.fa-spinner{animation:rotate 1s linear infinite;color:#fff}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .price-details .price-flight{font-size:32px;font-weight:500;line-height:40px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .price-details .price-flight .monedaConv{font-size:16px}.flights-results-wrapper .flight-info-card .flight-modal-info .modal-content-wrapper .flight-details-popup .popup-bottom .price-details .price-package{font-size:10px;font-weight:300;letter-spacing:.2px}}.spinner-wrapper{align-items:center;display:flex;flex-direction:column;gap:20px;height:100%;justify-content:center;padding:20px;width:100%}.spinner-wrapper .spinner{animation:rotate 1s linear infinite;border-radius:50%;height:48px;position:relative;width:48px}.spinner-wrapper .spinner:after,.spinner-wrapper .spinner:before{animation:prixClipFix 2s linear infinite;border:5px solid #fff;border-radius:50%;box-sizing:border-box;content:"";inset:0;position:absolute}.spinner-wrapper .spinner:after{animation:prixClipFix 2s linear infinite,rotate .5s linear infinite reverse;border-color:var(--booking_color_1,#333);inset:6px}@keyframes rotate{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}@keyframes prixClipFix{0%{clip-path:polygon(50% 50%,0 0,0 0,0 0,0 0,0 0)}25%{clip-path:polygon(50% 50%,0 0,100% 0,100% 0,100% 0,100% 0)}50%{clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,100% 100%,100% 100%)}75%{clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 100%)}to{clip-path:polygon(50% 50%,0 0,100% 0,100% 100%,0 100%,0 0)}}.modal-wrapper{align-items:center;background:#00000040;bottom:0;display:flex;justify-content:center;left:0;position:fixed;right:0;top:0;z-index:100}.modal-wrapper .modal-content-wrapper{max-height:90vh;overflow:auto;position:relative}.modal-wrapper .modal-content-wrapper .close-modal-button{color:#444;cursor:pointer;position:absolute;right:20px;top:17px;z-index:2}.modal-wrapper .modal-content-wrapper .close-modal-button i{font-size:32px}@media screen and (max-width:768px){.modal-wrapper .modal-content-wrapper{background:#fff;bottom:0;left:0;max-height:unset;overflow:auto;position:fixed;right:0;top:0}}.filters-wrapper{align-items:center;display:flex;justify-content:space-between;margin-bottom:16px}.filters-wrapper .left-panel .results-counter{font-size:16px;width:max-content}.filters-wrapper .right-panel{align-items:center;display:flex;gap:16px}.filters-wrapper .right-panel .price-order-filter{position:relative}.filters-wrapper .right-panel .price-order-filter .input-wrapper{border:1px solid #dfe0e1;border-radius:4px;cursor:pointer;padding:4px 8px;position:relative;width:max-content}.filters-wrapper .right-panel .price-order-filter .input-wrapper .label{background-color:#fff;color:#6e6e6e;font-size:10px;left:12px;letter-spacing:.25px;line-height:12px;padding:0 4px;position:absolute;top:-8px}.filters-wrapper .right-panel .price-order-filter .input-wrapper i{color:#6e6e6e;font-size:14px}.filters-wrapper .right-panel .price-order-filter .input-wrapper button{background-color:#0000;border:none;color:#6e6e6e;cursor:pointer;font-size:16px;padding-right:8px}.filters-wrapper .right-panel .price-order-filter .options-wrapper{background-color:#fff;border:1px solid #dfe0e1;border-radius:4px;bottom:-8px;display:flex;flex-direction:column;gap:8px;min-width:100%;padding:8px 16px;position:absolute;right:0;transform:translateY(100%);z-index:1}.filters-wrapper .right-panel .price-order-filter .options-wrapper .option{color:#6e6e6e;cursor:pointer;font-size:16px;width:max-content}.filters-wrapper .right-panel .price-order-filter .options-wrapper .option i{color:#6e6e6e;margin-left:8px}.filters-wrapper .right-panel .filters-panel-wrapper{border-left:1px solid #dfe0e1;position:relative}.filters-wrapper .right-panel .filters-panel-wrapper .panel-toggle{background-color:#0000;border:none;cursor:pointer;font-size:16px;letter-spacing:.4px;padding:8px 16px}.filters-wrapper .right-panel .filters-panel-wrapper .panel-toggle i{margin-right:8px}.filters-wrapper .right-panel .filters-panel-wrapper .panel-toggle i,.filters-wrapper .right-panel .filters-panel-wrapper .panel-toggle span{color:var(--booking_color_1,#456ba7)}.filters-wrapper .right-panel .filters-panel-wrapper .panel{background-color:#fff;border:1px solid #dfe0e1;border-radius:4px;bottom:-8px;display:flex;flex-direction:column;gap:8px;min-width:100%;padding:8px 16px;position:absolute;right:0;right:12px;transform:translateY(100%);width:528px;z-index:1}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category{padding:16px 8px 8px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-title{font-size:18px;font-weight:500;margin-bottom:12px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options{display:grid;gap:8px;grid-template-columns:1fr 1fr}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options .checkbox{accent-color:var(--text_color,#333);cursor:pointer;margin:0 8px 0 0}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options label{cursor:pointer}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container{align-items:center;display:flex;gap:4px;justify-content:start}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container .option-card{border:1px solid #cdcfd0;border-radius:4px;cursor:pointer;padding:8px;text-align:center}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container .option-card .card-icon{font-size:30px;margin-bottom:8px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container .option-card .card-text{display:flex;flex-direction:column;font-size:14px;line-height:19px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container .option-card .card-text span{font-size:10px;line-height:14px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .filter-options.card-container .option-card.selected{border-color:var(--booking_color_1,#456ba7)}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category:not(:first-of-type){border-top:1px solid #cdcfd0}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .slider-wrapper{margin:16px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .slider-wrapper .price-slider-range .rc-slider-handle-1,.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .slider-wrapper .price-slider-range .rc-slider-handle-2{margin-top:-4px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .prices-range-wrapper{align-items:center;display:flex;gap:40px;justify-content:space-between}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .prices-range-wrapper .input-elem{border:1px solid #dfe0e1;border-radius:4px;padding:8px 12px 4px;position:relative;width:50%}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .prices-range-wrapper .input-elem label{background:#fff;color:#6e6e6e;font-size:10px;left:10px;letter-spacing:.25px;line-height:12px;padding:0 5px;position:absolute;top:-5px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .prices-range-wrapper .input-elem span{font-size:16px}.filters-wrapper .right-panel .filters-panel-wrapper .panel .filter-category .price-filter .prices-range-wrapper .input-elem:first-of-type:after{background:#dfe0e1;content:"";height:1px;position:absolute;right:-40px;top:50%;transform:translate(-50%,-50%);width:20px}@media screen and (max-width:767px){.filters-wrapper .right-panel{justify-content:space-between;width:100%}.filters-wrapper .right-panel .price-order-filter .options-wrapper{left:0;right:auto}.filters-wrapper .right-panel .filters-panel-wrapper{border:none}.filters-wrapper .right-panel .filters-panel-wrapper .panel-toggle{padding:8px}.filters-wrapper .right-panel .filters-panel-wrapper .filters-panel .panel{border:none;border-radius:0;padding:48px 24px 24px;position:unset;transform:none;width:100%}.filters-wrapper .right-panel .filters-panel-wrapper .filters-panel .panel .filter-options{align-items:center;display:flex}.filters-wrapper .right-panel .filters-panel-wrapper .filters-panel .apply-filters-btn{background-color:#fff;border:1px solid var(--booking_color_1,#08c);border-radius:8px;color:var(--booking_color_1,#08c);cursor:pointer;display:block;font-size:16px;font-weight:700;margin:0 16px 24px auto;padding:8px 32px}}.rc-slider{border-radius:6px;height:14px;padding:5px 0;position:relative;touch-action:none;width:100%}.rc-slider,.rc-slider *{-webkit-tap-highlight-color:rgba(0,0,0,0);box-sizing:border-box}.rc-slider-rail{background-color:#e9e9e9;border-radius:6px;height:4px;position:absolute;width:100%}.rc-slider-track,.rc-slider-tracks{background-color:#abe2fb;border-radius:6px;height:4px;position:absolute}.rc-slider-track-draggable{background-clip:content-box;border-bottom:5px solid #0000;border-top:5px solid #0000;box-sizing:initial;transform:translateY(-5px);z-index:1}.rc-slider-handle{background-color:#fff;border:2px solid #96dbfa;border-radius:50%;cursor:pointer;cursor:-webkit-grab;cursor:grab;height:14px;margin-top:-5px;opacity:.8;position:absolute;touch-action:pan-x;width:14px;z-index:1}.rc-slider-handle-dragging.rc-slider-handle-dragging.rc-slider-handle-dragging{border-color:#57c5f7;box-shadow:0 0 0 5px #96dbfa}.rc-slider-handle:focus{box-shadow:none;outline:none}.rc-slider-handle:focus-visible{border-color:#2db7f5;box-shadow:0 0 0 3px #96dbfa}.rc-slider-handle-click-focused:focus{border-color:#96dbfa;box-shadow:unset}.rc-slider-handle:hover{border-color:#57c5f7}.rc-slider-handle:active{border-color:#57c5f7;box-shadow:0 0 5px #57c5f7;cursor:-webkit-grabbing;cursor:grabbing}.rc-slider-mark{font-size:12px;left:0;position:absolute;top:18px;width:100%}.rc-slider-mark-text{color:#999;cursor:pointer;display:inline-block;position:absolute;text-align:center;vertical-align:middle}.rc-slider-mark-text-active{color:#666}.rc-slider-step{background:#0000;height:4px;position:absolute;width:100%}.rc-slider-dot{background-color:#fff;border:2px solid #e9e9e9;border-radius:50%;bottom:-2px;cursor:pointer;height:8px;position:absolute;vertical-align:middle;width:8px}.rc-slider-dot-active{border-color:#96dbfa}.rc-slider-dot-reverse{margin-right:-4px}.rc-slider-disabled{background-color:#e9e9e9}.rc-slider-disabled .rc-slider-track{background-color:#ccc}.rc-slider-disabled .rc-slider-dot,.rc-slider-disabled .rc-slider-handle{background-color:#fff;border-color:#ccc;box-shadow:none;cursor:not-allowed}.rc-slider-disabled .rc-slider-dot,.rc-slider-disabled .rc-slider-mark-text{cursor:not-allowed!important}.rc-slider-vertical{height:100%;padding:0 5px;width:14px}.rc-slider-vertical .rc-slider-rail{height:100%;width:4px}.rc-slider-vertical .rc-slider-track{bottom:0;left:5px;width:4px}.rc-slider-vertical .rc-slider-track-draggable{border-bottom:0;border-left:5px solid #0000;border-right:5px solid #0000;border-top:0;transform:translateX(-5px)}.rc-slider-vertical .rc-slider-handle{margin-left:-5px;margin-top:0;position:absolute;touch-action:pan-y;z-index:1}.rc-slider-vertical .rc-slider-mark{height:100%;left:18px;top:0}.rc-slider-vertical .rc-slider-step{height:100%;width:4px}.rc-slider-vertical .rc-slider-dot{margin-left:-2px}.rc-slider-tooltip-zoom-down-appear,.rc-slider-tooltip-zoom-down-enter,.rc-slider-tooltip-zoom-down-leave{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;display:block!important}.rc-slider-tooltip-zoom-down-appear.rc-slider-tooltip-zoom-down-appear-active,.rc-slider-tooltip-zoom-down-enter.rc-slider-tooltip-zoom-down-enter-active{animation-name:rcSliderTooltipZoomDownIn;animation-play-state:running}.rc-slider-tooltip-zoom-down-leave.rc-slider-tooltip-zoom-down-leave-active{animation-name:rcSliderTooltipZoomDownOut;animation-play-state:running}.rc-slider-tooltip-zoom-down-appear,.rc-slider-tooltip-zoom-down-enter{animation-timing-function:cubic-bezier(.23,1,.32,1);transform:scale(0)}.rc-slider-tooltip-zoom-down-leave{animation-timing-function:cubic-bezier(.755,.05,.855,.06)}@keyframes rcSliderTooltipZoomDownIn{0%{opacity:0;transform:scale(0);transform-origin:50% 100%}to{transform:scale(1);transform-origin:50% 100%}}@keyframes rcSliderTooltipZoomDownOut{0%{transform:scale(1);transform-origin:50% 100%}to{opacity:0;transform:scale(0);transform-origin:50% 100%}}.rc-slider-tooltip{left:-9999px;position:absolute;top:-9999px;visibility:visible}.rc-slider-tooltip,.rc-slider-tooltip *{-webkit-tap-highlight-color:rgba(0,0,0,0);box-sizing:border-box}.rc-slider-tooltip-hidden{display:none}.rc-slider-tooltip-placement-top{padding:4px 0 8px}.rc-slider-tooltip-inner{background-color:#6c6c6c;border-radius:6px;box-shadow:0 0 4px #d9d9d9;color:#fff;font-size:12px;height:24px;line-height:1;min-width:24px;padding:6px 2px;text-align:center;text-decoration:none}.rc-slider-tooltip-arrow{border-color:#0000;border-style:solid;height:0;position:absolute;width:0}.rc-slider-tooltip-placement-top .rc-slider-tooltip-arrow{border-top-color:#6c6c6c;border-width:4px 4px 0;bottom:4px;left:50%;margin-left:-4px}.step-wrapper{margin:8px auto;width:1140px}.step-wrapper .step-toggle{align-items:center;background-color:#fff;border:2px solid #4d4f5c;border-radius:12px;display:flex;justify-content:space-between;padding:16px 32px}.step-wrapper .step-toggle:not(.disabled){cursor:pointer}.step-wrapper .step-toggle.disabled{opacity:.5}.step-wrapper .step-toggle .step-content .step-name{font-size:20px;font-weight:700;letter-spacing:.4px}.step-wrapper .step-toggle .step-content .step-price{color:var(--booking_color_1,#456ba7);font-size:20px;font-weight:700;letter-spacing:.4px;margin:0 4px}.step-wrapper .step-toggle .step-content .step-icon{color:#3ab793;font-size:24px;margin-left:8px}.step-wrapper .step-toggle i{color:#7a7e81;font-size:26px}@media screen and (max-width:767px){.step-wrapper{width:calc(100% - 20px)}.step-wrapper .step-toggle{padding:16px}.step-wrapper .step-toggle .step-content .step-name,.step-wrapper .step-toggle .step-content .step-price{font-size:16px;letter-spacing:.32px}.step-wrapper .step-toggle .step-content .step-icon{font-size:20px}.step-wrapper .step-toggle i{font-size:19px}.step-wrapper .step-content{width:100%}}.extras-wrapper{background-color:#fff;border:1px solid #d7dae2;border-radius:12px;margin:16px 0;padding:24px 32px}.extras-wrapper .top-info{align-items:center;display:flex;justify-content:space-between}.extras-wrapper .top-info .extras-title{font-size:18px;font-weight:700;letter-spacing:.36px}.extras-wrapper .top-info .extras-details{align-items:center;display:flex;gap:16px;justify-content:end}.extras-wrapper .top-info .extras-details .extra{align-items:center;display:flex;gap:8px;justify-content:center}.extras-wrapper .top-info .extras-details .extra svg.cabin-luggage-included,.extras-wrapper .top-info .extras-details .extra svg.checked-luggage-included,.extras-wrapper .top-info .extras-details .extra svg.hand-luggage-included{width:24px}.extras-wrapper .top-info .extras-details .extra svg.cabin-luggage-not-included,.extras-wrapper .top-info .extras-details .extra svg.checked-luggage-not-included,.extras-wrapper .top-info .extras-details .extra svg.hand-luggage-not-included{width:28px}.extras-wrapper .top-info .extras-details .extra span{font-size:12px;letter-spacing:.24px}.extras-wrapper .top-info .extras-details .extra span span{display:block;font-size:10px;letter-spacing:.2px}.extras-wrapper .extras-message{font-size:16px;letter-spacing:.32px;line-height:20px;margin-top:24px}.extras-wrapper .extras-selection-wrapper{display:flex;flex-direction:column;gap:16px;margin-top:24px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line{align-items:center;display:flex;gap:32px;justify-content:start}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .label{color:var(--text_color,#333);font-size:16px;font-weight:500;min-width:fit-content;text-transform:none}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container{align-items:center;border:1px solid #d7dae2;border-radius:12px;display:flex;flex-wrap:wrap;gap:16px;justify-content:flex-start;max-width:100%;padding:12px 0;width:max-content}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector{align-items:center;display:flex;gap:8px;padding:0 24px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector:not(:last-of-type){border-right:1px solid #d7dae2}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .description-wrapper{align-items:center;display:flex;gap:12px;justify-content:center}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .description-wrapper .icon{font-size:24px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .description-wrapper .weight-info .price-tag,.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .description-wrapper .weight-info .up-to{display:block;font-size:10px;text-transform:capitalize}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .description-wrapper .weight-info .max-weight-allowed{display:block;font-size:16px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper{align-items:center;display:flex;justify-content:center}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .sign-element{background-color:#0000;border:none;cursor:pointer}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .sign-element i{font-size:11px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .sign-element[disabled]{cursor:default}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .sign-element[disabled] i{opacity:.5}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .quantity-input{appearance:none;-webkit-appearance:none;background:#0000;border:none;font-size:18px;font-weight:700;padding:0;text-align:center;width:32px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector .selector-wrapper .quantity-input:focus-visible{outline:none}.extras-subtotal-wrapper{font-size:20px;font-weight:700;letter-spacing:.4px;margin:16px 0;text-align:right;width:100%}.extras-subtotal-wrapper .subtotal-label{color:var(--text_color,#333)}.extras-subtotal-wrapper .subtotal-price{color:var(--booking_color_1,#456ba7)}@media screen and (max-width:767px){.extras-wrapper .extras-selection-wrapper .extra-passenger-line{flex-direction:column;gap:12px}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container{flex-direction:column;width:100%}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector{justify-content:space-between;padding:0;width:calc(100% - 48px)}.extras-wrapper .extras-selection-wrapper .extra-passenger-line .extras-container .extra-selector:not(:last-of-type){border-bottom:1px solid #d7dae2;border-right:none;padding-bottom:12px}.extras-subtotal-wrapper{font-size:16px;letter-spacing:.32px}}.passengers-form-wrapper{margin:24px 0 16px}.passengers-form-wrapper .passenger-wrapper:not(:last-of-type){margin-bottom:24px}.passengers-form-wrapper .passenger-wrapper .title{font-size:16px;font-weight:500;line-height:26px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper{display:flex;flex-wrap:wrap;gap:16px 8px;margin-top:16px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container{border:1px solid #dfe0e1;border-radius:4px;min-width:250px;padding:8px 12px;position:relative;width:calc(25% - 6px)}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container.validation-error{border:2px solid #dc6767}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container.validation-error label{color:#dc6767}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container.validation-error:before{background-color:#dc6767;border-radius:50%;bottom:-6px;color:#fff;content:"";font-family:Font Awesome\ 5 Pro;font-size:10px;font-weight:700;height:17px;line-height:17px;position:absolute;right:-6px;text-align:center;width:17px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container label{background-color:#fff;color:var(--text_color,#333);font-size:10px;left:6px;letter-spacing:.25px;line-height:12px;padding:0 4px;position:absolute;top:-8px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container input,.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select{background-color:#0000;border:none;width:100%}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container input:focus-visible,.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select:focus-visible{outline:none}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container input[disabled],.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container input[readOnly],.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select[disabled],.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select[readOnly]{opacity:.5}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select{appearance:none;-webkit-appearance:none}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container.has-arrow:after{color:#6e6e6e;content:"";font-family:Font Awesome\ 5 Pro;font-size:14px;position:absolute;right:8px;top:8px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input{display:grid;grid-template-areas:"prefix number";grid-template-columns:30% 70%;position:relative}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input:after{color:#6e6e6e;content:"";font-family:Font Awesome\ 5 Pro;font-size:14px;left:23%;pointer-events:none;position:absolute;top:4px;z-index:1}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper{cursor:pointer;grid-area:prefix}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .prefix-label-wrapper{height:100%;width:100%}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper{background-color:#fff;border:1px solid #dfe0e1;bottom:-10px;left:-12px;position:absolute;transform:translateY(100%);width:calc(100% + 24px);z-index:1}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper .search_input{border:none;color:var(--text_color,#333);font-size:10px;letter-spacing:.25px;padding:8px 24px 8px 8px;width:100%}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper .options-wrapper{max-height:150px;overflow-y:scroll}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper .options-wrapper .option{font-size:12px;margin:4px 8px}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper .options-wrapper .option.selected,.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input .prefix-selector-wrapper .dropdown-wrapper .options-wrapper .option:hover{background-color:#dfe0e1}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input input[name=selected-prefix]{cursor:pointer}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container .phone-prefix-input input[name=contact_number]{border-left:1px solid #dfe0e1;grid-area:number;padding-left:12px}@media screen and (max-width:767px){.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container{width:100%}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container input,.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select{height:auto;margin-bottom:0;min-height:100%;padding:0}.passengers-form-wrapper .passenger-wrapper .form-fields-wrapper .field-container select{background:none}.passengers-form-wrapper .subtotal-wrapper .subtotal{padding:0}}.subtotal-wrapper{gap:48px;margin:32px auto 16px;width:1140px}.subtotal-wrapper,.subtotal-wrapper .subtotal{align-items:center;display:flex;justify-content:end}.subtotal-wrapper .subtotal{gap:24px}.subtotal-wrapper .subtotal span{font-size:16px;letter-spacing:.6px}.subtotal-wrapper .subtotal .price-wrapper .currencyValue,.subtotal-wrapper .subtotal .price-wrapper .monedaConv{font-size:40px;font-weight:700;letter-spacing:1.6px}.subtotal-wrapper .continue-booking-btn{background-color:var(--booking_color_1,#08c);border:none;border-radius:10px;color:#fff;cursor:pointer;font-size:20px;padding:16px 24px}.subtotal-wrapper .continue-booking-btn[disabled]{cursor:default;opacity:.5}.subtotal-wrapper .continue-booking-btn span{margin-right:8px}.subtotal-wrapper .continue-booking-btn:hover:not([disabled]){opacity:.8}@media screen and (max-width:767px){.subtotal-wrapper{border-top:1px solid #dfe0e1;flex-direction:column;gap:16px;justify-content:center;padding-top:16px;width:calc(100% - 20px)}.subtotal-wrapper .subtotal{gap:32px;padding:0 16px}.subtotal-wrapper .subtotal .price-wrapper .currencyValue,.subtotal-wrapper .subtotal .price-wrapper .monedaConv{font-size:32px}.subtotal-wrapper .continue-booking-btn{width:100%}}