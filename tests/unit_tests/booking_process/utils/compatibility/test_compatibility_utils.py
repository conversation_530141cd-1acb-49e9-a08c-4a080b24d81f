import json

from booking_process.utils.compatibility.compatibility_utils import dumps_json_for_javascript
from tests.test_base import TestBase


class TestCompatibilityUtils(TestBase):

    def test_dumps_json_for_javascript(self):
        with self.subTest('Simple dictionary'):
            test_data = {"key": "value", "number": 123}
            result = dumps_json_for_javascript(test_data)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # Should be valid JSON after template literal processing
            parsed = json.loads(js_processed)
            self.assertEqual(parsed, test_data)

        with self.subTest('Double quotes in string'):
            test_data = {"property_name": '<PERSON>a balinesa "Fresquito" '}
            result = dumps_json_for_javascript(test_data)

            # Should have double-escaped quotes for JavaScript template literals
            self.assertIn('\\\\"', result)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # After template literal processing, should be valid JSON
            parsed = json.loads(js_processed)
            self.assertEqual(parsed, test_data)

        with self.subTest('Single quotes in string'):
            test_data = {"property_name": "Text with 'single quotes'"}
            result = dumps_json_for_javascript(test_data)

            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)

        with self.subTest('Newlines and tabs'):
            test_data = {"multiline": "Text with\nnewlines\tand\ttabs"}
            result = dumps_json_for_javascript(test_data)

            # Should be valid JSON
            parsed = json.loads(result)

            # Whitespace should be replaced with spaces
            self.assertNotIn('\n', result)
            self.assertNotIn('\t', result)
            self.assertIn(' ', parsed["multiline"])

        with self.subTest('Unicode line separators'):
            test_data = {"text": "Text with\u2028line\u2029separators"}
            result = dumps_json_for_javascript(test_data)

            # Should be valid JSON
            parsed = json.loads(result)

            # Unicode separators should be replaced with spaces
            self.assertNotIn('\u2028', result)
            self.assertNotIn('\u2029', result)

        with self.subTest('Backticks escaping'):
            test_data = {"with_backticks": "Text with `backticks` here"}
            result = dumps_json_for_javascript(test_data)

            # Backticks should be escaped in the result
            self.assertIn('\\\\`', result)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # After template literal processing, should be valid JSON
            parsed = json.loads(js_processed)

            # Parsed data should contain escaped backticks (this is intentional for safety)
            self.assertIn('\\`', parsed["with_backticks"])

        with self.subTest('Mixed dangerous content'):
            test_data = {
                "complex": 'Mixed "quotes" with `backticks` and\nnewlines',
                "simple": "Normal text",
                "number": 123,
                "boolean": True,
                "null_value": None
            }
            result = dumps_json_for_javascript(test_data)

            # Check that all dangerous characters are handled in the raw output
            self.assertNotIn('\n', result)
            self.assertIn('\\\\`', result)
            self.assertIn('\\\\"', result)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # After template literal processing, should be valid JSON
            parsed = json.loads(js_processed)

            # Verify that dangerous characters are properly escaped (data modification is intentional for safety)
            self.assertIn('\\`', parsed["complex"])
            self.assertIn('"', parsed["complex"])
            self.assertEqual(parsed["simple"], test_data["simple"])
            self.assertEqual(parsed["number"], test_data["number"])
            self.assertEqual(parsed["boolean"], test_data["boolean"])
            self.assertEqual(parsed["null_value"], test_data["null_value"])

        with self.subTest('Empty content'):
            test_data = {}
            result = dumps_json_for_javascript(test_data)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # Should be valid JSON after template literal processing
            parsed = json.loads(js_processed)
            self.assertEqual(parsed, test_data)

        with self.subTest('Nested structures'):
            test_data = {
                "nested": {
                    "level1": {
                        "level2": "Text with `backticks`"
                    }
                },
                "array": ["item1", {"key": "value with `backticks`"}]
            }
            result = dumps_json_for_javascript(test_data)

            self.assertIn('\\\\`', result)

            # Simulate JavaScript template literal processing
            js_processed = result.replace('\\\\"', '\\"')

            # Should be valid JSON after template literal processing
            parsed = json.loads(js_processed)

            # Verify nested escaping is preserved (data modification is intentional for safety)
            self.assertIn('\\`', parsed["nested"]["level1"]["level2"])
            self.assertIn('\\`', parsed["array"][1]["key"])
